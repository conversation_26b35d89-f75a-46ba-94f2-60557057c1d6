/**
 * Created by k<PERSON><PERSON><PERSON> on 06/05/2021.
 */

 public with sharing class ContractTriggerHandler implements ITriggerHandler {
  public static Boolean triggerDisabled = false;

  public Boolean isDisabled() {
    if (UtilsBypass.canTrigger('ContractTrigger'))
      return triggerDisabled;
    else
      return true;
  }

  public void beforeInsert(List<SObject> newItems) {
    APER12_Contract_Management.setContractName(Trigger.new);

    //VJOY 14/11/2023 - Populate Address Fields for RO Client
    APER12_Contract_Management.updateAddressFields(Trigger.new);

    //VJOY 16/11/2023 - Calculate EndDate for RO Client
    APER12_Contract_Management.updateContractEndDateRO(Trigger.new, null);
  }

  public void afterInsert(Map<Id, SObject> newItems, Map<Id, SObject> oldItems) {
    //ALK 31/05/2021 - Link the new contract to the quote signed (docusign pdf) document.
    APER12_Contract_Management.linkContractToSignedDocument((Map<Id, Contract>) Trigger.newMap);

    //AAZ 29/08/2022 - activate smarter contracts created via Quote API
    APER12_Contract_Management.activateContract(Trigger.new);

    //CWA 22/09/2022- Task creation on opportunity
    APER12_Contract_Management.contractTaskCreation(Trigger.new);

    //AAZ 17/01/2023 - map some ZQuote fields to the linked Contract
    APER12_Contract_Management.mapFieldsFromZQuote(Trigger.new);
  }

  public void beforeUpdate(Map<Id, SObject> newItems, Map<Id, SObject> oldItems) {
    APER12_Contract_Management.validateIfContractDateOverlap(
      (Map<Id, Contract>) newItems,
      (Map<Id, Contract>) oldItems
    );

      //VJOY 16/11/2023 - Calculate EndDate for RO Client
    APER12_Contract_Management.updateContractEndDateRO(Trigger.new, (Map<Id, Contract>) oldItems);
  }

  public void afterUpdate(Map<Id, SObject> newItems, Map<Id, SObject> oldItems) {
    // Create tasks
    APER12_Contract_Management.createTasksAfterUpdate((Map<Id, Contract>) newItems, (Map<Id, Contract>) oldItems);

    APER12_Contract_Management.createZuoraSubscription((Map<Id, Contract>) newItems, (Map<Id, Contract>) oldItems);
    APER12_Contract_Management.endZuoraSubscription((Map<Id, Contract>) newItems, (Map<Id, Contract>) oldItems);

    APER12_Contract_Management.activateAccount((Map<Id, Contract>) newItems, (Map<Id, Contract>) oldItems);
    //OLA Separate Close Opportunity from Manage SLI
    APER12_Contract_Management.closeOpportunity((Map<Id, Contract>) newItems, (Map<Id, Contract>) oldItems);

    APER20_Account_Management.deactivateAccountsStatusForTerminatedContracts(((Map<Id, Contract>) newItems).keySet());

    APER39_DiscountCode_Mangement.deactivateAffiliationCodesForTerminatedContracts(
      ((Map<Id, Contract>) newItems).keySet()
    );

    //AAZ - Mark Objects to be synced with Smarter
    APER12_Contract_Management.markObjectsToBeSyncedWithSmarter(Trigger.new, (Map<Id, Contract>) oldItems);

    //Noor - Sync Account to Smarter when contract is activated
    APER12_Contract_Management.syncAccountToSmarter((Map<Id, Contract>) newItems, (Map<Id, Contract>) oldItems);

    //DSA 12/12/2023 - Sync Account,Opp and other objects for Contracts created in SF, synchronized and activated in BON 
    APER12_Contract_Management.syncObjectsForActivatedContractFromBON(Trigger.new, (Map<Id, Contract>) oldItems); 
  }

  public void beforeDelete(Map<Id, SObject> oldItems) {
    APER12_Contract_Management.processContractBeforeDelete((Map<Id, Contract>) oldItems);
  }

  public void afterDelete(Map<Id, SObject> oldItems) {
  }

  public void afterUndelete(Map<Id, SObject> oldItems) {
  }
}
