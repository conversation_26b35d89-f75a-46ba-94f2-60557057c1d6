<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>DataDotComAccountInsights</excludeButtons>
    <excludeButtons>DataDotComClean</excludeButtons>
    <excludeButtons>DisableCustomerPortalAccount</excludeButtons>
    <excludeButtons>DisablePartnerPortalAccount</excludeButtons>
    <excludeButtons>IncludeOffline</excludeButtons>
    <excludeButtons>RequestUpdate</excludeButtons>
    <excludeButtons>SendEmail</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <feedLayout>
        <autocollapsePublisher>true</autocollapsePublisher>
        <compactFeed>true</compactFeed>
        <feedFilterPosition>CenterDropDown</feedFilterPosition>
        <feedFilters>
            <feedFilterType>AllUpdates</feedFilterType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>TextPost</feedItemType>
        </feedFilters>
        <fullWidthFeed>true</fullWidthFeed>
        <hideSidebar>false</hideSidebar>
        <highlightExternalFeedItems>true</highlightExternalFeedItems>
        <rightComponents>
            <componentType>Following</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Followers</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Topics</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomLinks</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomButtons</componentType>
        </rightComponents>
        <useInlineFiltersInConsole>true</useInlineFiltersInConsole>
    </feedLayout>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Account Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Legal_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Legal_Form__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Old_Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Customer_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Registration_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Legal_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_VAT_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Not_subject_to_VAT__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Tax_Office__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HU_HQ_Contact__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Other_Fiscal_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SK_RegistrationNumberText__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERBE_First_transaction_date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Merchant_Master_FinancialCenter__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Client_Master_FinancialCenter__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERRO_Annual_Turnover__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERRO_Profit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERRO_Founding_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERRO_Operational_Status__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_BUPicklist__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountSource</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Account_SubSource__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERBE_Has_a_Social_secretary__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERBE_Social_secretary_needed_for_Orders__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Insolvency_Executed_Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Decision_maker__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Aggregator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Aggregator_Id__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Inactive_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Inactive_date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_First_visit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ERRO_Area__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERRO_Area_Manager__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Ownership</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Credit Scoring information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Credit_Scoring__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Credit_Limit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_ProviderDescription__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Business_Risk__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Last_Credit_Scoring_Update__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ERBE_Joint_Commissions__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CZ_Gift__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ER_Country_Code__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Marketing Segmentations</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Global_Segmentation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Local_Merchant_Segmentation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Segment__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_ABC_Client_Segmentation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Industry__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Number_of_stores__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_RFM_Client_Score__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Client_Priority__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_VIP__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Merchant_coverage__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_NaceList__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_NaceNameList__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NumberOfEmployees</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Employee_Size_Range__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_RFM_Client_Persona__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Merchant Financial Informations</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Monthly_redemption_volume__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_YTD_redemption_volume__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Number_of_transactions__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Last_Transaction_date__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Revenues_monthly__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_YTD_Revenues__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Take_up_rate__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Client Bill Collection</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Bill_Collector__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Total_Amount_Due__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Outstanding_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Bill_Collector_Action__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DSO__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ER_Total_Annual_Billed_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Synchronization Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Sync_result__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Creation_date_in_OS__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Card_Operational_System_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Card_Client_Operational_System_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_MDMId__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_WS_result_Text__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Last_date_of_Sync__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Paper_Operational_System_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Paper_Client_Operational_System_ID__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns>
            <!--layoutItems>
                <customLink>CZ_Contracts_And_Solutions</customLink>
            </layoutItems-->
        </layoutColumns>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ER_Last_Modified_By_Functional_user__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.PollPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.QuestionPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendEmail</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ER_GetPublicRegister</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ER_GetCreditScoring</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Generate_Leads</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.ER_Create_FC</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AccountHierarchy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>14</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewTask</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>SendEmail</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <component>runtime_sales_social:socialPanel</component>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>ER_Role__c</fields>
        <fields>CONTACT.PHONE1</fields>
        <fields>CONTACT.PHONE3</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>ER_Status__c</fields>
        <fields>ER_VIP__c</fields>
        <fields>ER_Email_Opt_Out__c</fields>
        <fields>ER_Sync_result__c</fields>
        <relatedList>RelatedContactList</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassAddToCampaign</excludeButtons>
        <excludeButtons>Merge</excludeButtons>
        <excludeButtons>NewContact</excludeButtons>
        <fields>CONTACT.FULL_NAME</fields>
        <fields>CONTACT.ACCOUNT_NAME</fields>
        <fields>ACCCONRELATION.IS_DIRECT</fields>
        <fields>ACCCONRELATION.IS_ACTIVE</fields>
        <fields>ACCCONRELATION.ROLES</fields>
        <fields>ACCCONRELATION.START_DATE</fields>
        <fields>ACCCONRELATION.END_DATE</fields>
        <relatedList>RelatedAccountContactRelationList</relatedList>
        <sortField>ACCCONRELATION.IS_DIRECT</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.RECORDTYPE</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>ER_Solutions__c</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
        <sortField>OPPORTUNITY.NAME</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_User__c</fields>
        <fields>ER_Status__c</fields>
        <fields>ER_Priority__c</fields>
        <relatedList>ER_Preferred_Agent__c.ER_Account__c</relatedList>
        <sortField>ER_Priority__c</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <relatedLists>
        <customButtons>ER_Case_Accept</customButtons>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>CASES.STATUS</fields>
        <fields>CASES.CREATED_DATE_DATE_ONLY</fields>
        <fields>OWNER_NAME</fields>
        <fields>CASES.RECORDTYPE</fields>
        <relatedList>RelatedCaseList</relatedList>
        <sortField>CASES.CREATED_DATE_DATE_ONLY</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_Solution__c</fields>
        <fields>ER_Global_Status__c</fields>
        <fields>ER_Scope__c</fields>
        <fields>ER_Contract__c</fields>
        <fields>ER_Sync_result__c</fields>
        <relatedList>ER_Solution_Line_Item__c.ER_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>CONTRACT.CONTRACT_NUMBER</fields>
        <fields>CONTRACT.NAME</fields>
        <fields>ER_ZuoraQuote__c</fields>
        <fields>Subscription__c</fields>
        <fields>CONTRACT.STATUS</fields>
        <fields>CONTRACT.START_DATE</fields>
        <fields>CONTRACT.END_DATE</fields>
        <fields>ER_OpportunityId__c</fields>
        <fields>ER_Solutions__c</fields>
        <relatedList>RelatedContractList</relatedList>
        <sortField>CONTRACT.CONTRACT_NUMBER</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>ORDERS.ORDER_NUMBER</fields>
        <fields>ER_External_ID__c</fields>
        <fields>ER_Data_Extract__c</fields>
        <fields>ER_Evolution_YTD__c</fields>
        <fields>ER_Revenues_YTD__c</fields>
        <fields>ER_Revenues_N_1__c</fields>
        <relatedList>RelatedOrderList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>CAMPAIGN.CAMPAIGN_TYPE</fields>
        <fields>CAMPAIGN.START_DATE</fields>
        <fields>CAMPAIGN.BUDGETED_COST</fields>
        <fields>CAMPAIGN.STATUS</fields>
        <relatedList>RelatedAccountCampaignList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>CAMPAIGN.START_DATE</fields>
        <fields>CAMPAIGN.CAMPAIGN_TYPE</fields>
        <fields>CM.STATUS</fields>
        <fields>CM.RESPONDED</fields>
        <fields>CM.LAST_UPDATE</fields>
        <relatedList>RelatedCampaignList</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_Competitor__c</fields>
        <fields>ER_Competitor_Solution__c</fields>
        <fields>ER_Contract_end_date__c</fields>
        <fields>ER_Opportunity__c</fields>
        <fields>ER_Price__c</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>ER_Competitor__c.ER_Company__c</relatedList>
        <sortField>CREATED_DATE</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_National_ID_Number__c</fields>
        <fields>ER_Date_of_birth__c</fields>
        <fields>ER_Employee_Status__c</fields>
        <fields>ER_Personal_Email__c</fields>
        <relatedList>ER_Employee__c.ER_Client__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_Type__c</fields>
        <fields>ER_Card_Operational_System_ID__c</fields>
        <fields>ER_Contact__c</fields>
        <fields>ER_Bank_Account__c</fields>
        <fields>ER_Master__c</fields>
        <fields>ER_Sync_result__c</fields>
        <relatedList>ER_Financial_Center__c.ER_Account_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_Status__c</fields>
        <fields>ER_Contact__c</fields>
        <fields>ER_Street__c</fields>
        <fields>ER_City__c</fields>
        <fields>ER_Zip_Code__c</fields>
        <fields>ER_Type__c</fields>
        <fields>ER_Card_Operational_System_ID__c</fields>
        <fields>ER_Sync_result__c</fields>
        <relatedList>ER_Store__c.ER_Merchant__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_Street__c</fields>
        <fields>ER_City__c</fields>
        <fields>ER_Delivery_Contact__c</fields>
        <fields>ER_Sync_result__c</fields>
        <relatedList>ER_Delivery_Site__c.ER_Account_Name__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>ER_Loop__c.NAME</fields>
        <fields>ER_Behaviour__c</fields>
        <relatedList>ER_Account_to_Loop__c.ER_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>ACCOUNT.NAME</fields>
        <fields>PARTNER.ROLE</fields>
        <fields>ER_Legal_Name__c</fields>
        <fields>ER_Registration_Number__c</fields>
        <fields>ER_VAT_Number__c</fields>
        <relatedList>RelatedPartnerList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedAccountContactRoleList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>MEMBER_NAME</fields>
        <fields>TEAM_MEMBER_ROLE</fields>
        <relatedList>RelatedAccountSalesTeam</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>CREATED_DATE</fields>
        <fields>Zuora__Credit_Balance__c</fields>
        <fields>Zuora__BillCycleDay__c</fields>
        <fields>Zuora__BillToContact__c</fields>
        <fields>Zuora__Status__c</fields>
        <relatedList>Zuora__CustomerAccount__c.Zuora__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>zqu__SubscriptionTermStartDate__c</fields>
        <fields>zqu__SubscriptionType__c</fields>
        <fields>zqu__Subscription_Term_Type__c</fields>
        <fields>zqu__PaymentTerm__c</fields>
        <fields>zqu__PaymentMethod__c</fields>
        <fields>zqu__Status__c</fields>
        <fields>zqu__BillToContact__c</fields>
        <fields>zqu__Opportunity__c</fields>
        <fields>OWNER.ALIAS</fields>
        <relatedList>zqu__Quote__c.zqu__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>ER_Master__c</fields>
        <fields>ER_Parent_Subscription__c</fields>
        <fields>Zuora__CustomerAccount__c</fields>
        <fields>ER_Contract__c</fields>
        <fields>Zuora__SubscriptionStartDate__c</fields>
        <fields>Zuora__TermSettingType__c</fields>
        <fields>Zuora__Status__c</fields>
        <fields>Zuora__Version__c</fields>
        <fields>Zuora__AutoRenew__c</fields>
        <relatedList>Zuora__Subscription__c.Zuora__Account__c</relatedList>
        <sortField>NAME</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Zuora__InvoiceDate__c</fields>
        <fields>ER_Order_Number__c</fields>
        <fields>ER_Transaction_Type__c</fields>
        <fields>Zuora__TotalAmount__c</fields>
        <fields>Zuora__PaymentAmount__c</fields>
        <fields>Zuora__Balance2__c</fields>
        <fields>ER_Purchase_Order_Reference__c</fields>
        <fields>Zuora__BillingAccount__c</fields>
        <fields>Zuora__Status__c</fields>
        <relatedList>Zuora__ZInvoice__c.Zuora__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Zuora__SubmittedOn__c</fields>
        <fields>Zuora__Effective_Date__c</fields>
        <fields>ER_Order_Number__c</fields>
        <fields>Zuora__PaymentMethod__c</fields>
        <fields>Zuora__Amount__c</fields>
        <fields>Zuora__BankIdentificationNumber__c</fields>
        <fields>Zuora__Type__c</fields>
        <fields>Zuora__BillingAccount__c</fields>
        <fields>Zuora__Status__c</fields>
        <relatedList>Zuora__Payment__c.Zuora__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <runAssignmentRulesDefault>true</runAssignmentRulesDefault>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>true</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h1t0000049e8o</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>2</sizeY>
        <summaryLayoutItems>
            <field>ER_Registration_Number__c</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>ER_Legal_Form__c</field>
            <posX>0</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>ER_VIP__c</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>ER_Status__c</field>
            <posX>1</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>ER_Insolvency_Executed_Account__c</field>
            <posX>2</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
