<?xml version="1.0" encoding="UTF-8"?>
<LeadConvertSettings xmlns="http://soap.sforce.com/2006/04/metadata">
    <allowOwnerChange>true</allowOwnerChange>
    <objectMapping>
        <inputObject>Lead</inputObject>
        <mappingFields>
            <inputField>ER_Tax_Office__c</inputField>
            <outputField>ER_Tax_Office__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_BUPicklist__c</inputField>
            <outputField>ER_BUPicklist__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Registration_Number__c</inputField>
            <outputField>ER_Registration_Number__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Credit_Limit__c</inputField>
            <outputField>ER_Credit_Limit__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Credit_Scoring__c</inputField>
            <outputField>ER_Credit_Scoring__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Industry__c</inputField>
            <outputField>ER_Industry__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Last_Credit_Scoring_Update__c</inputField>
            <outputField>ER_Last_Credit_Scoring_Update__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Legal_Country_Code__c</inputField>
            <outputField>ER_Country_Code__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Legal_Form__c</inputField>
            <outputField>ER_Legal_Form__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Legal_Name__c</inputField>
            <outputField>ER_Legal_Name__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Legal_Status__c</inputField>
            <outputField>ER_Legal_Status__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_NaceList__c</inputField>
            <outputField>ER_NaceList__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_NaceNameList__c</inputField>
            <outputField>ER_NaceNameList__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Not_Subject_to_VAT__c</inputField>
            <outputField>ER_Not_subject_to_VAT__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Number_of_outlets__c</inputField>
            <outputField>ER_Number_of_stores__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Other_Fiscal_ID__c</inputField>
            <outputField>ER_Other_Fiscal_ID__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_VAT_Number__c</inputField>
            <outputField>ER_VAT_Number__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Business_Risk__c</inputField>
            <outputField>ER_Business_Risk__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_SmartER_Context__c</inputField>
            <outputField>ER_SmartER_Context__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_SmartER_Original_Lead_Id__c</inputField>
            <outputField>ER_SmartER_Original_Lead_Id__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Employee_Size_Range__c</inputField>
            <outputField>ER_Employee_Size_Range__c</outputField>
        </mappingFields>
        <outputObject>Account</outputObject>
    </objectMapping>
    <objectMapping>
        <inputObject>Lead</inputObject>
        <mappingFields>
            <inputField>ER_BUPicklist__c</inputField>
            <outputField>ER_BUPicklist__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Do_not_mail__c</inputField>
            <outputField>ER_Do_Not_Mail__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Language__c</inputField>
            <outputField>ER_Language__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Legal_Country__c</inputField>
            <outputField>ER_Legal_Country__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Legal_Country_Code__c</inputField>
            <outputField>ER_Legal_Country_Code__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Lead_Channel__c</inputField>
            <outputField>ER_Channel__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Role__c</inputField>
            <outputField>ER_Role__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_SmartER_Context__c</inputField>
            <outputField>ER_SmartER_Context__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_SmartER_Original_Lead_Id__c</inputField>
            <outputField>ER_SmartER_Original_Lead_Id__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Commercial_Consent__c</inputField>
            <outputField>ER_Commercial_Consent__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Inquiries__c</inputField>
            <outputField>ER_Inquiries__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Relational_Communication__c</inputField>
            <outputField>ER_Relational_Communication__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Sync_to_HubSpot__c</inputField>
            <outputField>ER_Sync_to_HubSpot__c</outputField>
        </mappingFields>
        <outputObject>Contact</outputObject>
    </objectMapping>
    <objectMapping>
        <inputObject>Lead</inputObject>
        <mappingFields>
            <inputField>ER_EShop__c</inputField>
            <outputField>ER_DocuGen_E_Shop__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Lead_Requestor__c</inputField>
            <outputField>ER_Lead_Requestor__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Lead_Type__c</inputField>
            <outputField>ER_Lead_Source_Type__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Number_Of_Beneficiaries__c</inputField>
            <outputField>ER_Estimated_Number_Of_Beneficiaries__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Product_Family_Interest__c</inputField>
            <outputField>ER_Product_Family__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>TECH_PromoCheck__c</inputField>
            <outputField>TECH_PromoCheck__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_UTM_tag__c</inputField>
            <outputField>ER_UTM_tag__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_BUPicklist__c</inputField>
            <outputField>ER_BUPicklist__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_Face_Value__c</inputField>
            <outputField>ER_Face_Value__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_SmartER_Context__c</inputField>
            <outputField>ER_SmartER_Context__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_SmartER_Original_Lead_Id__c</inputField>
            <outputField>ER_SmartER_Original_Lead_Id__c</outputField>
        </mappingFields>
        <mappingFields>
            <inputField>ER_No_Competitor__c</inputField>
            <outputField>ER_No_Competitor__c</outputField>
        </mappingFields>
        <outputObject>Opportunity</outputObject>
    </objectMapping>
    <opportunityCreationOptions>VisibleOptional</opportunityCreationOptions>
</LeadConvertSettings>
