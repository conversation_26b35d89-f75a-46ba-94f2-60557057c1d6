import os
import shutil
import sys
import xml.etree.ElementTree as ET
from xml.dom import minidom

from dotenv import load_dotenv

from Package_list import deploymen_packages
from UnitTest import print_file_paths
from changedFiles import save_to_txt
from deployment import deploy_to_salesforce
from pullRequestStatus import pullRequest_completion

load_dotenv()
current_directory = os.getcwd()

matching_rules = deploymen_packages()


def merge_pr_branch_into_main(pr_source_branch):
    """Merge PR branch into main to get the combined state for full deployment"""
    import subprocess
    try:
        print(f"📢 Merging {pr_source_branch} into main for full deployment...")
        sys.stdout.flush()

        # Merge the PR branch into main
        merge_command = f"git merge origin/{pr_source_branch} --no-edit"
        result = subprocess.run(merge_command, stdout=subprocess.PIPE,
                                shell=True, text=True, stderr=subprocess.PIPE)

        if result.returncode != 0:
            print(f"⚠️ Git merge warning/info: {result.stderr}")
            # Don't fail on merge warnings, continue

        print(f"✅ Successfully merged {pr_source_branch} into main")
        sys.stdout.flush()

    except Exception as e:
        print(f"⛔ Error merging branches: {str(e)}")
        sys.stdout.flush()
        raise


def get_all_source_files():
    """Get all source files from src/ directory for full deployment"""
    current_directory = os.path.dirname(os.path.abspath(__file__))
    src_directory = os.path.join(current_directory, '..', 'src')
    all_files = []

    # Check if src directory exists
    if not os.path.exists(src_directory):
        print(f"⛔ Source directory not found: {src_directory}")
        return all_files

    # Walk through all files in src directory
    for root, _, files in os.walk(src_directory):
        for file in files:
            # Get relative path from project root
            file_path = os.path.relpath(os.path.join(
                root, file), os.path.join(current_directory, '..'))
            # Convert Windows paths to Unix-style paths for consistency
            file_path = file_path.replace('\\', '/')
            # Only include files that start with 'src/' (Salesforce package files)
            if file_path.startswith('src/'):
                all_files.append(file_path)

    print(f"📢 Found {len(all_files)} source files for full deployment")
    sys.stdout.flush()
    return all_files


def main():
    PR_ID = os.environ.get('SYSTEM_PULLREQUEST_PULLREQUESTID')
    PR_SOURCE_BRANCH = os.environ.get(
        'SYSTEM_PULLREQUEST_SOURCEBRANCH').split("/")[-1]
    TEAM_PROJECT = os.environ.get('SYSTEM_TEAMPROJECT')
    BUILD_REPOSITORY_ID = os.environ.get('BUILD_REPOSITORY_ID')
    PR_STATUS = pullRequest_completion(
        TEAM_PROJECT, BUILD_REPOSITORY_ID, PR_ID)
    DELTA_DEPLOYMENT = os.environ.get(
        'DELTA_DEPLOYMENT', 'true').lower() == 'true'
    print(f"DELTA_DEPLOYMENT: {DELTA_DEPLOYMENT}")

    sys.stdout.flush()

    if PR_STATUS not in ["succeeded", "queued"]:
        print(
            f"⛔ Deployement not started due one of the following merge reasong : \n [ conflicts, failure, rejectedByPolicy, notSet ]")
        sys.stdout.flush()
        sys.exit()

    print("Pull request Status 🆗")
    sys.stdout.flush()

    if DELTA_DEPLOYMENT:
        print("Delta deployment is enabled. Finding changed files between branches...")
        sys.stdout.flush()

        changedFiles = save_to_txt('origin/main', f"origin/{PR_SOURCE_BRANCH}")

        if not changedFiles:
            print(
                "‼️ No change found : please check the pull request and target branch files")
            sys.stdout.flush()
            sys.exit()

        with open('build/changed_files.txt', 'r') as file:
            lines_list = file.read().splitlines()

        build_package_xml(lines_list, matching_rules)
    else:
        print("Delta deployment is disabled. Deploying full package (dev branch + PR changes)...")
        sys.stdout.flush()

        # First, merge the PR branch into main to get the combined state
        merge_pr_branch_into_main(PR_SOURCE_BRANCH)

        # Get all files from the current state (dev + PR changes merged)
        all_files = get_all_source_files()

        if not all_files:
            print("‼️ No source files found for full deployment")
            sys.stdout.flush()
            sys.exit()

        # Build package.xml based on all existing source files
        build_package_xml(all_files, matching_rules)


def build_package_xml(changed_files, matching_rules):
    os.makedirs(os.path.dirname("build/src/package.xml"), exist_ok=True)
    current_directory = os.path.dirname(os.path.abspath(__file__))
    root = ET.Element(
        "Package", xmlns="http://soap.sforce.com/2006/04/metadata")
    version_element = ET.SubElement(root, "version")
    version_element.text = "56.0"

    for file_path in changed_files:
        if file_path.startswith('src/'):
            source_path = os.path.join(current_directory, '..', file_path)
            destination_path = os.path.join(
                current_directory, '../build', file_path)

            os.makedirs(os.path.dirname(destination_path), exist_ok=True)

            shutil.copyfile(source_path, destination_path)

        fSplit = file_path.split("/")[-1]
        _, file_extension = os.path.splitext(fSplit)

        for rule in matching_rules:
            if file_extension == rule["extension"]:
                types = ET.SubElement(root, "types")
                member = ET.SubElement(types, "members")
                member.text = "*"
                name = ET.SubElement(types, "name")
                name.text = rule["Package_name"]
                break

    xml_str = ET.tostring(root, encoding='utf-8').decode('utf-8')
    parsed_xml = minidom.parseString(xml_str)
    formatted_xml = parsed_xml.toprettyxml(indent="  ")
    with open("build/src/package.xml", "w") as file:
        file.write(formatted_xml)

    toprtty = parsed_xml.toprettyxml()
    print(f"✅ Generated XML : \n {toprtty}")
    sys.stdout.flush()

    print_file_paths("build/src")

    deploy_to_salesforce()


if __name__ == "__main__":
    main()
