<?xml version="1.0" encoding="UTF-8" ?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <customSettingsType>Hierarchy</customSettingsType>
    <description>Used to grant bypass access to VR by Profiles and Users</description>
    <enableFeeds>false</enableFeeds>
    <label>Validation Rules Settings</label>
    <visibility>Public</visibility>
    <fields>
        <fullName>ERRO01_Prevent_Person_Account_Changing__c</fullName>
        <defaultValue>false</defaultValue>
        <description>Bypass VR ERRO01_Prevent_Person_Account_Changing</description>
        <externalId>false</externalId>
        <label>ERRO01_Prevent_Person_Account_Changing</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Asset_Actions_Before_Update__c</fullName>
        <defaultValue>false</defaultValue>
        <description>Bypass flow ER_Asset_Actions_Before_Update</description>
        <externalId>false</externalId>
        <label>ER_Asset_Actions_Before_Update</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Last_Modified_Date_Functional_User__c</fullName>
        <defaultValue>false</defaultValue>
        <description
    >When this checkbox is ticked this means the field ER_Last_Modified_Date_Functional_User__c won&apos;t be overridden by the selected Profile/User</description>
        <externalId>false</externalId>
        <label>ER_Last_Modified_Date_Functional_User__c</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE02_Valid_Phone__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE02_Valid_Phone</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE03_Valid_VATnr__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE03_Valid_VATnr</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE05_VAT_Not_Blank__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE05_VAT_Not_Blank</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE06_Valid_Email__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE06_Valid_Email</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE07_Valid_Mobile__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE07_Valid_Mobile</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE08_Valid_Phone__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE08_Valid_Phone</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE09_Valid_Mobile__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE09_Valid_Mobile</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE10_Valid_Email__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE10_Valid_Email</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE11_Valid_Phone__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE11_Valid_Phone</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE13_Valid_VATnr__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE13_Valid_VATnr</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE14_VAT_Not_Blank__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE14_VAT_Not_Blank</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRER001_AssetlName_AccountLookup__c</fullName>
        <defaultValue>false</defaultValue>
        <description>Field to bypass the VR VRER001_AssetlName_AccountLookup</description>
        <externalId>false</externalId>
        <label>VRER001_AssetlName_AccountLookup</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRER001_Employee_FullName__c</fullName>
        <defaultValue>false</defaultValue>
        <description>This Custom field is used to bypass ER_Employee__c.VRER001_Employee_FullName VR</description>
        <externalId>false</externalId>
        <label>VRER001_Employee_FullName</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRER01_Order_Read_Only__c</fullName>
        <defaultValue>true</defaultValue>
        <externalId>false</externalId>
        <label>VRER01_Order_Read_Only</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRER01_BillingAccount_ReadOnly__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRER01_BillingAccount_ReadOnly</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRBE04_Prevent_Selecting_Pricebook_BELU__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRBE04_Prevent_Selecting_Pricebook_BELU</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRER05_Case_Queue_BusinessUnit__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRER05_Case_Queue_BusinessUnit</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRER07_Prevent_Selecting_Std_Pricebook__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>VRER07_Prevent_Selecting_Std_Pricebook</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>VRRO01_Prevent_Person_Account_Changing__c</fullName>
        <defaultValue>false</defaultValue>
        <description
    >This field is created to bypass teh following VR Account.VRRO01_Prevent_Person_Account_Changing</description>
        <externalId>false</externalId>
        <label>VRRO01_Prevent_Person_Account_Changing</label>
        <trackTrending>false</trackTrending>
        <type>Checkbox</type>
    </fields>
</CustomObject>
