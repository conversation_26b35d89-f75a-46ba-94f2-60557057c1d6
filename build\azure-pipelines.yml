trigger:
  branches:
    exclude:
      - '*'

pr:
  branches:
    include:
      - main

jobs:
  - job: RunPythonScript
    workspace:
      clean: outputs
    variables:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      # Use workspace-level directory instead of source directory
      PIP_CACHE_DIR: $(Pipeline.Workspace)/.pip
    steps:
      - checkout: self
        fetchDepth: 0
        displayName: 'Fetch All Branches'

      - script: |
          git switch main
        displayName: 'Switching to main branch'

      # Create cache directory AFTER checkout
      - bash: |
          mkdir -p $(PIP_CACHE_DIR)
        displayName: 'Create PIP cache directory'

      # Fixed Cache task - moved AFTER directory creation and checkout
      - task: Cache@2
        displayName: PIP Cache
        inputs:
          key: 'pip | "$(Agent.OS)" | "$(Build.SourcesDirectory)/build/requirements.txt"'
          restoreKeys: |
            pip | "$(Agent.OS)"
          path: $(PIP_CACHE_DIR)  # Use workspace directory

      - bash: az devops configure --defaults organization=$(System.TeamFoundationCollectionUri) project=$(System.TeamProject) --use-git-aliases true
        displayName: 'Set default Azure DevOps organization and project'

      - task: UsePythonVersion@0
        inputs:
          versionSpec: '3.11'
          addToPath: true

      - script: |
          python -m venv env
          source env/bin/activate || . env/bin/activate
          python -m pip install --upgrade pip
          # Use the cached directory explicitly
          pip install --cache-dir $(PIP_CACHE_DIR) -r $(Build.SourcesDirectory)/build/requirements.txt
        displayName: 'Install requirements'

      - script: |
          source env/bin/activate || . env/bin/activate
          python $(Build.SourcesDirectory)/build/main.py
        env:
          DOMAIN: $(DOMAIN)
          OAUTH_ENDPOINT: $(OAUTH_ENDPOINT)
          USER_NAME: $(USER_NAME)
          PASSWORD: $(PASSWORD)
          SECURITY_TOKEN: $(SECURITY_TOKEN)
          CONSUMER_KEY: $(CONSUMER_KEY)
          CONSUMER_SECRET: $(CONSUMER_SECRET)
          EMAIL_HOST: $(EMAIL_HOST)
          EMAIL_PORT: $(EMAIL_PORT)
          EMAIL_HOST_USER: $(EMAIL_HOST_USER)
          EMAIL_HOST_PASSWORD: $(EMAIL_HOST_PASSWORD)
          SYSTEM_ACCESSTOKEN: $(SYSTEM_ACCESSTOKEN)
          RELEASE_MANAGER: $(RELEASE_MANAGER)
          EMAIL: $(EMAIL)
          JIRA_TOKEN: $(JIRA_TOKEN)
        displayName: "SF Deployment & PR Completion"