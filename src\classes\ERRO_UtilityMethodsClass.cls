/*
----------------------------------------------------------------------
-- - Name          : ERRO_UtilityMethodsClass
-- - Author        : snica
-- - Description   : Uitlity Methods - This class is used to group all the functionnality linked to ERRO
--
-- Date         Name                Version     Remarks
-- -----------  -----------         --------    ---------------------------------------
--  Nov-2019    snica               1.0         Initial version - added methods for CAEN Code & labels calculation
---------------------------------------------------------------------------------------
*/
public class ERRO_UtilityMethodsClass {
    
    /**
   * <AUTHOR>
   * @date 14/11/2023
   * @description ERRO get all CAEN code labels
   */
  @AuraEnabled
  public static List<String> getCAENCodeLabelsERRO() {
    Map<String, String> caenMap = new Map<String, String>();
    List<String> caenList = new List<String>();
    StaticResource sr = [SELECT Id, Body FROM StaticResource WHERE Name = 'ERROCaen' LIMIT 1];
    String caenCodes = sr.Body.toString();
    String caenLabels;

    List<String> bodyContent = caenCodes.split('/////////');

    for(String str: bodyContent){
      caenList.add(str.split('=')[1]);
    }
  return caenList;
  }


  /**
   * <AUTHOR>
   * @date 15/11/2023
   * @description ERRO update Nace Name List with CAEN code labels
   */
  @AuraEnabled
  public static void updateCAENFieldsERRO(String caenLabel, String recordId) {

    Id idToUpdate = recordId;
    Schema.SObjectType sobjectType = idToUpdate.getSObjectType();
    String sObjectName = sobjectType.getDescribe().getName();

    String query = 'SELECT Id, ER_NaceList__c';
    query = query + ' FROM ' + sObjectName + ' WHERE Id = ' + '\'' + recordId + '\'';

    List<sObject> recordsToUpdate = Database.query(query);

    List<SObject> objectlist = new List<SObject>();
    for(SObject obj: recordsToUpdate){
        obj.put('ER_NaceList__c', caenLabel);
        objectlist.add(obj);
    }
    update objectlist;

  }

  /**
   * <AUTHOR>
   * @date 14/11/2023
   * @description ERRO Calculate Nace list(caen code) based on caen label
   * @param sObject
   */
  public static void updateNaceListERRO(List<sObject> obj) {
    Map<String, String> caenMap = new Map<String, String>();
    List<sObject> objectsList = new List<sObject>();
    StaticResource sr = [SELECT Id, Body FROM StaticResource WHERE Name = 'ERROCaen' LIMIT 1];
    String caenCodes = sr.Body.toString();

    List<String> bodyContent = caenCodes.split('/////////');

    for(String str: bodyContent){
      caenMap.put(str.split('=')[1], str.split('=')[0]);
    }
    for (sObject eachObj : obj) {
      objectsList.add(eachObj);
    }
    if (!objectsList.isEmpty()) {
      for (sObject eachObj : objectsList) {
        if (eachObj.get('ER_NaceList__c').toString() != '') {
          String caenLabel = (eachObj.get('ER_NaceList__c')).toString();
          if (eachObj.get('ER_NaceList__c') != '') {
            eachObj.put('ER_NaceNameList__c', caenMap.get(caenLabel));
          }
        }  
      }
    }
  }

  /**
   * <AUTHOR>
   * @date 15/11/2023
   * @description Check VAT Syntax based conditions for RO
   * @param valueVat, record sObject, fieldName
   * @param
   */
  public static String CheckVATNumber(String valueVAT, SObject record) {
  valueVAT = valueVAT.trim();
  String cif = valueVAT;
  String mask = '7532175321';

  if (valueVAT.startsWith('RO')) {
    cif = valueVAT.substring(2);
  } else if (cif.length() > 10) {
    record.addError('VAT number is too long.');
    return '';
  }

  String inverse = '';
  for (Integer i = cif.length() - 1; i >= 0; i--) {
    inverse += cif.substring(i, i + 1);
  }

  Integer sum = 0;
  try {
    mask = mask.substring(10 - inverse.length());
    for (Integer i = 0; i < inverse.length() - 1; i++) {
      sum += Integer.valueOf(mask.substring(i, i + 1)) * Integer.valueOf(cif.substring(i, i + 1));
    }
  } catch (Exception e) {
    record.addError('VAT number is too long.');
    return '';
  }

  Integer prod = Integer.valueOf(Math.mod(sum * 10, 11));
  if (prod == 10) {
    prod = 0;
  }

  if (prod != Integer.valueOf(cif.substring(cif.length() - 1))) {
    record.addError('Invalid VAT number, please check and type it again.');
  } else {
    return cif;
    // VAT OK
  }

  return '';
  }
}