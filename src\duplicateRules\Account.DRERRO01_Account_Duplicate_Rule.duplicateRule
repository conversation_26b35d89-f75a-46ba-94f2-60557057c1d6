<?xml version="1.0" encoding="UTF-8"?>
<DuplicateRule xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionOnInsert>Block</actionOnInsert>
    <actionOnUpdate>Block</actionOnUpdate>
    <alertText>Use one of these records?</alertText>
    <description xsi:nil="true"/>
    <duplicateRuleFilter>
        <booleanFilter xsi:nil="true"/>
        <duplicateRuleFilterItems>
            <field>ER_BUPicklist__c</field>
            <operation>equals</operation>
            <value>RO</value>
            <sortOrder>1</sortOrder>
            <table>Account</table>
        </duplicateRuleFilterItems>
        <duplicateRuleFilterItems>
            <field>AccountNumber</field>
            <operation>notEqual</operation>
            <value></value>
            <sortOrder>2</sortOrder>
            <table>Account</table>
        </duplicateRuleFilterItems>
    </duplicateRuleFilter>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Account</matchRuleSObjectType>
        <matchingRule>MRERRO01_AccountMatchingRule</matchingRule>
        <objectMapping xsi:nil="true"/>
    </duplicateRuleMatchRules>
    <isActive>true</isActive>
    <masterLabel>DRERRO01_Account_Duplicate_Rule</masterLabel>
    <securityOption>EnforceSharingRules</securityOption>
    <sortOrder>2</sortOrder>
</DuplicateRule>