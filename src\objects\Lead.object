<?xml version="1.0" encoding="UTF-8" ?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>AddToCampaign</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ChangeStatus</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Convert</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>FindDup</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ListClean</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Lead_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <businessProcesses>
        <fullName>Client Lead Process</fullName>
        <isActive>true</isActive>
        <values>
            <fullName>New</fullName>
            <default>true</default>
        </values>
        <values>
            <fullName>Workable</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Qualification</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Qualified</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Rejected</fullName>
            <default>false</default>
        </values>
    </businessProcesses>
    <businessProcesses>
        <fullName>Merchant Lead Process</fullName>
        <isActive>true</isActive>
        <values>
            <fullName>New</fullName>
            <default>true</default>
        </values>
        <values>
            <fullName>Workable</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Qualification</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Qualified</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Rejected</fullName>
            <default>false</default>
        </values>
    </businessProcesses>
    <compactLayoutAssignment>ER_Merchant_Leads_Custom_Compact_Layout</compactLayoutAssignment>
    <compactLayouts>
        <fullName>ER_Client_Leads_Custom_Compact_Layout</fullName>
        <fields>Name</fields>
        <fields>Company</fields>
        <fields>Title</fields>
        <fields>Phone</fields>
        <fields>Email</fields>
        <label>Client Leads Custom Compact Layout</label>
    </compactLayouts>
    <compactLayouts>
        <fullName>ER_Merchant_Leads_Custom_Compact_Layout</fullName>
        <fields>Name</fields>
        <fields>Company</fields>
        <fields>Rating</fields>
        <fields>ER_Role__c</fields>
        <fields>MobilePhone</fields>
        <fields>Phone</fields>
        <fields>Email</fields>
        <fields>OwnerId</fields>
        <fields>RecordTypeId</fields>
        <label>Merchant Leads Custom Compact Layout</label>
    </compactLayouts>
    <compactLayouts>
        <fullName>Leads_Custom_Compact_Layout</fullName>
        <fields>Name</fields>
        <fields>Company</fields>
        <fields>Title</fields>
        <fields>Phone</fields>
        <fields>MobilePhone</fields>
        <fields>Email</fields>
        <label>Leads Custom Compact Layout</label>
    </compactLayouts>
    <enableFeeds>true</enableFeeds>
    <enableHistory>true</enableHistory>
    <fields>
        <fullName>Address</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>AnnualRevenue</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>CampaignId</fullName>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Company</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
    </fields>
    <fields>
        <fullName>ER_Country_Code__c</fullName>
        <defaultValue>$User.ER_BUText__c</defaultValue>
        <externalId>false</externalId>
        <label>Country Code</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetName>ER_Country_Code</valueSetName>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Promo_Code__c</fullName>
        <externalId>false</externalId>
        <label>Discount Code</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>CurrencyIsoCode</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Description</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <inlineHelpText
    >Make sure your comments are objective, adequate, and limited to what is strictly necessary. They must not contain sensitive data (e.g., in relation to health/medical, religion, political opinions, racial or ethnic origins, etc.) or data related to the commission of criminal offences. Appropriate and respectful language is expected</inlineHelpText>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>DoNotCall</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ERBE_Initial_Product_Of_Interest__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Initial Product Of Interest</label>
        <lookupFilter>
            <active>true</active>
            <filterItems>
                <field>$Source.ER_BUPicklist__c</field>
                <operation>equals</operation>
                <valueField>Product2.ER_BUPicklist__c</valueField>
            </filterItems>
            <filterItems>
                <field>Product2.IsActive</field>
                <operation>equals</operation>
                <value>True</value>
            </filterItems>
            <isOptional>false</isOptional>
        </lookupFilter>
        <referenceTo>Product2</referenceTo>
        <relationshipLabel>LeadsPrd</relationshipLabel>
        <relationshipName>LeadsPrd</relationshipName>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>ERCZ_Academic_Title__c</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <externalId>false</externalId>
        <label>Academic Title</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>as.</fullName>
                    <default>false</default>
                    <label>as.</label>
                </value>
                <value>
                    <fullName>CSc.</fullName>
                    <default>false</default>
                    <label>CSc.</label>
                </value>
                <value>
                    <fullName>DiS.</fullName>
                    <default>false</default>
                    <label>DiS.</label>
                </value>
                <value>
                    <fullName>doc.</fullName>
                    <default>false</default>
                    <label>doc.</label>
                </value>
                <value>
                    <fullName>Dr.</fullName>
                    <default>false</default>
                    <label>Dr.</label>
                </value>
                <value>
                    <fullName>DrSc.</fullName>
                    <default>false</default>
                    <label>DrSc.</label>
                </value>
                <value>
                    <fullName>DSc.</fullName>
                    <default>false</default>
                    <label>DSc.</label>
                </value>
                <value>
                    <fullName>Ing.</fullName>
                    <default>false</default>
                    <label>Ing.</label>
                </value>
                <value>
                    <fullName>Ing. arch.</fullName>
                    <default>false</default>
                    <label>Ing. arch.</label>
                </value>
                <value>
                    <fullName>JUDr.</fullName>
                    <default>false</default>
                    <label>JUDr.</label>
                </value>
                <value>
                    <fullName>MDDr.</fullName>
                    <default>false</default>
                    <label>MDDr.</label>
                </value>
                <value>
                    <fullName>MgA.</fullName>
                    <default>false</default>
                    <label>MgA.</label>
                </value>
                <value>
                    <fullName>Mgr.</fullName>
                    <default>false</default>
                    <label>Mgr.</label>
                </value>
                <value>
                    <fullName>MSDr.</fullName>
                    <default>false</default>
                    <label>MSDr.</label>
                </value>
                <value>
                    <fullName>MUDr.</fullName>
                    <default>false</default>
                    <label>MUDr.</label>
                </value>
                <value>
                    <fullName>MVDr.</fullName>
                    <default>false</default>
                    <label>MVDr.</label>
                </value>
                <value>
                    <fullName>odb. as.</fullName>
                    <default>false</default>
                    <label>odb. as.</label>
                </value>
                <value>
                    <fullName>PaedDr.</fullName>
                    <default>false</default>
                    <label>PaedDr.</label>
                </value>
                <value>
                    <fullName>Ph.D.</fullName>
                    <default>false</default>
                    <label>Ph.D.</label>
                </value>
                <value>
                    <fullName>PharmDr.</fullName>
                    <default>false</default>
                    <label>PharmDr.</label>
                </value>
                <value>
                    <fullName>PhDr.</fullName>
                    <default>false</default>
                    <label>PhDr.</label>
                </value>
                <value>
                    <fullName>PhMr.</fullName>
                    <default>false</default>
                    <label>PhMr.</label>
                </value>
                <value>
                    <fullName>prof.</fullName>
                    <default>false</default>
                    <label>prof.</label>
                </value>
                <value>
                    <fullName>RCDr.</fullName>
                    <default>false</default>
                    <label>RCDr.</label>
                </value>
                <value>
                    <fullName>RNDr.</fullName>
                    <default>false</default>
                    <label>RNDr.</label>
                </value>
                <value>
                    <fullName>RSDr.</fullName>
                    <default>false</default>
                    <label>RSDr.</label>
                </value>
                <value>
                    <fullName>RTDr.</fullName>
                    <default>false</default>
                    <label>RTDr.</label>
                </value>
                <value>
                    <fullName>Th.D.</fullName>
                    <default>false</default>
                    <label>Th.D.</label>
                </value>
                <value>
                    <fullName>ThDr.</fullName>
                    <default>false</default>
                    <label>ThDr.</label>
                </value>
                <value>
                    <fullName>ThLic.</fullName>
                    <default>false</default>
                    <label>ThLic.</label>
                </value>
                <value>
                    <fullName>ThMgr.</fullName>
                    <default>false</default>
                    <label>ThMgr.</label>
                </value>
                <value>
                    <fullName>pi</fullName>
                    <default>false</default>
                    <label>pi</label>
                </value>
                <value>
                    <fullName>pan</fullName>
                    <default>false</default>
                    <label>pan</label>
                </value>
                <value>
                    <fullName>paní</fullName>
                    <default>false</default>
                    <label>paní</label>
                </value>
                <value>
                    <fullName>p.</fullName>
                    <default>false</default>
                    <label>p.</label>
                </value>
                <value>
                    <fullName>pí</fullName>
                    <default>false</default>
                    <label>pí</label>
                </value>
                <value>
                    <fullName>pí.</fullName>
                    <default>false</default>
                    <label>pí.</label>
                </value>
                <value>
                    <fullName>Bc.</fullName>
                    <default>false</default>
                    <label>Bc.</label>
                </value>
                <value>
                    <fullName>BcA.</fullName>
                    <default>false</default>
                    <label>BcA.</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_AID_Authorization__c</fullName>
        <externalId>false</externalId>
        <label>AID Authorization</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_AID_Clearing__c</fullName>
        <externalId>false</externalId>
        <label>AID Clearing</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_AID_MID__c</fullName>
        <externalId>false</externalId>
        <label>AID_MID</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_AID__c</fullName>
        <externalId>false</externalId>
        <label>AID</label>
        <length>40</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Additional_Services__c</fullName>
        <description>Depending on the Edenred Solution, one or more additional services can be selected.</description>
        <externalId>false</externalId>
        <inlineHelpText
    >Depending on the Edenred Solution, one or more additional services can be selected.</inlineHelpText>
        <label>Additional Services</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>ER_BUPicklist__c</controllingField>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>MyOrder</fullName>
                    <default>false</default>
                    <label>MyOrder</label>
                </value>
            </valueSetDefinition>
            <valueSettings>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <valueName>MyOrder</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Amount_of_Transactions__c</fullName>
        <externalId>false</externalId>
        <label>Amount of Transactions</label>
        <precision>18</precision>
        <required>false</required>
        <scale>2</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Currency</type>
    </fields>
    <fields>
        <fullName>ER_Average_Amount_of_Transactions__c</fullName>
        <externalId>false</externalId>
        <label>Average Amount of Transactions</label>
        <precision>18</precision>
        <required>false</required>
        <scale>2</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Currency</type>
    </fields>
    <fields>
        <fullName>ER_BUPicklist__c</fullName>
        <defaultValue>$User.ER_BUText__c</defaultValue>
        <externalId>false</externalId>
        <label>Business Unit</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetName>ER_Business_Unit</valueSetName>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Business_Unit__c</fullName>
        <externalId>false</externalId>
        <formula>TEXT(Owner:User.ER_Business_Unit__c)</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Business Unit Formula</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Business_Risk__c</fullName>
        <complianceGroup>GDPR</complianceGroup>
        <externalId>false</externalId>
        <formula>IF(ISPICKVAL(ER_BUPicklist__c,&apos;FR&apos;),
            IF((ER_Credit_Scoring__c &lt; 30), IMAGE(&quot;/resource/BusinessRisk_RedFlag&quot;, &quot;&quot;,20,20), IF(( ER_Credit_Scoring__c &gt;= 30 &amp;&amp; ER_Credit_Scoring__c &lt;= 70), IMAGE(&quot;/resource/BusinessRisk_Orange&quot;, &quot;&quot;,20,20), IF(( ER_Credit_Scoring__c &gt;= 11), IMAGE(&quot;/resource/BusinessRisk_GreenFlag&quot;, &quot;&quot;,20,20), &apos;&apos;))),

            IF((ER_Credit_Scoring__c &lt;= 7), IMAGE(&quot;/resource/BusinessRisk_RedFlag&quot;, &quot;&quot;,20,20), IF(( ER_Credit_Scoring__c &gt;= 8 &amp;&amp; ER_Credit_Scoring__c &lt;= 10), IMAGE(&quot;/resource/BusinessRisk_Orange&quot;, &quot;&quot;,20,20), IF(( ER_Credit_Scoring__c &gt;= 11), IMAGE(&quot;/resource/BusinessRisk_GreenFlag&quot;, &quot;&quot;,20,20), &apos;&apos;)))
            )</formula>
        <label>Business Risk</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Credit_Limit__c</fullName>
        <complianceGroup>GDPR</complianceGroup>
        <externalId>false</externalId>
        <label>Credit Limit</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Credit_Scoring__c</fullName>
        <complianceGroup>GDPR</complianceGroup>
        <description>Credit Scoring functionality</description>
        <externalId>false</externalId>
        <label>Credit Scoring</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Last_Credit_Scoring_Update__c</fullName>
        <externalId>false</externalId>
        <label>Last Credit Scoring Update</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Date</type>
    </fields>
    <fields>
        <fullName>ER_Do_not_mail__c</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>Do Not Mail</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_EShop__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>E-Shop</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Email_Opt_Out_Logo__c</fullName>
        <complianceGroup>GDPR</complianceGroup>
        <externalId>false</externalId>
        <formula
    >IF(HasOptedOutOfEmail=true,IMAGE(&apos;/resource/ER_Email_Opt_Out&apos;,&apos;ER_Email_Opt_Out&apos;,40,40),&quot;&quot;)</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Email Opt Out</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Employee_Size_Range__c</fullName>
        <externalId>false</externalId>
        <label>Employee Size Range</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_IsPublicRegisterDeployedBU__c</fullName>
        <externalId>false</externalId>
        <formula
    >CONTAINS($CustomMetadata.Global_Settings__mdt.Public_Register_Deployed_BUs.Valueforformula__c, &quot;;&quot; &amp; TEXT(ER_BUPicklist__c) &amp; &quot;;&quot;)
|| $CustomMetadata.Global_Settings__mdt.Public_Register_Deployed_BUs.Valueforformula__c = &quot;ALL&quot;</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Is Public Register Deployed BU</label>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Is_Credit_Score_Deployed_BU__c</fullName>
        <externalId>false</externalId>
        <formula
    >CONTAINS($CustomMetadata.Global_Settings__mdt.CreditScoring_Deployed_BUs.Valueforformula__c, &quot;;&quot; &amp; TEXT(ER_BUPicklist__c) &amp; &quot;;&quot;)
            || $CustomMetadata.Global_Settings__mdt.CreditScoring_Deployed_BUs.Valueforformula__c = &quot;ALL&quot;</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Is Credit Score Deployed BU</label>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Lead_Channel__c</fullName>
        <externalId>false</externalId>
        <label>Lead Channel</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Client</fullName>
                    <default>false</default>
                    <label>Client</label>
                </value>
                <value>
                    <fullName>Merchant</fullName>
                    <default>false</default>
                    <label>Merchant</label>
                </value>
                <value>
                    <fullName>User</fullName>
                    <default>false</default>
                    <label>User</label>
                </value>
                <value>
                    <fullName>Customer</fullName>
                    <default>false</default>
                    <label>Customer</label>
                </value>
                <value>
                    <fullName>Internal</fullName>
                    <default>false</default>
                    <label>Internal</label>
                </value>
                <value>
                    <fullName>Lead provider</fullName>
                    <default>false</default>
                    <label>Lead provider</label>
                </value>
                <value>
                    <fullName>Trade association</fullName>
                    <default>false</default>
                    <label>Trade association</label>
                </value>
                <value>
                    <fullName>Other</fullName>
                    <default>false</default>
                    <label>Other</label>
                </value>
                <value>
                    <fullName>Unmatched</fullName>
                    <default>false</default>
                    <label>Unmatched</label>
                </value>
                <value>
                    <fullName>Auto Import</fullName>
                    <default>false</default>
                    <label>Auto Import</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Lead_Type__c</fullName>
        <externalId>false</externalId>
        <label>Lead Type</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Merchant</fullName>
                    <default>false</default>
                    <label>Merchant</label>
                </value>
                <value>
                    <fullName>Client</fullName>
                    <default>false</default>
                    <label>Client</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_External_Reference__c</fullName>
        <caseSensitive>false</caseSensitive>
        <externalId>true</externalId>
        <label>External Reference</label>
        <length>100</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <type>Text</type>
        <unique>true</unique>
    </fields>
    <fields>
        <fullName>ER_Face_Value__c</fullName>
        <description>Facial Value of the mealvouchers purchased by clients</description>
        <externalId>false</externalId>
        <label>Face Value</label>
        <precision>18</precision>
        <required>false</required>
        <scale>2</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>TECH_PromoCheck__c</fullName>
        <defaultValue>true</defaultValue>
        <externalId>false</externalId>
        <label>TECH_PromoCheck</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Lead_sub_source__c</fullName>
        <externalId>false</externalId>
        <label>Lead sub-source</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>LeadSource</controllingField>
            <restricted>false</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Adwords: Google, Bing</fullName>
                    <default>false</default>
                    <label>Adwords: Google, Bing</label>
                </value>
                <value>
                    <fullName>Other adwords</fullName>
                    <default>false</default>
                    <label>Other adwords</label>
                </value>
                <value>
                    <fullName>Lead provider</fullName>
                    <default>false</default>
                    <label>Lead provider</label>
                </value>
                <value>
                    <fullName>Public Register - list of existing companies</fullName>
                    <default>false</default>
                    <label>Public Register - list of existing companies</label>
                </value>
                <value>
                    <fullName>Public Register - list of new created companies</fullName>
                    <default>false</default>
                    <label>Public Register - list of new created companies</label>
                </value>
                <value>
                    <fullName>Abandonned contracting</fullName>
                    <default>false</default>
                    <label>Abandonned contracting</label>
                </value>
                <value>
                    <fullName>Internal Webform</fullName>
                    <default>false</default>
                    <label>Internal Webform</label>
                </value>
                <value>
                    <fullName>Encore</fullName>
                    <default>false</default>
                    <label>Encore</label>
                </value>
                <value>
                    <fullName>Newsletter</fullName>
                    <default>false</default>
                    <label>Newsletter</label>
                </value>
                <value>
                    <fullName>AdStrategy</fullName>
                    <default>false</default>
                    <label>AdStrategy</label>
                </value>
                <value>
                    <fullName>Companeo</fullName>
                    <default>false</default>
                    <label>Companeo</label>
                </value>
                <value>
                    <fullName>etc</fullName>
                    <default>false</default>
                    <label>etc</label>
                </value>
                <value>
                    <fullName>Meta</fullName>
                    <default>false</default>
                    <label>Meta</label>
                </value>
                <value>
                    <fullName>Linkedin</fullName>
                    <default>false</default>
                    <label>Linkedin</label>
                </value>
                <value>
                    <fullName>TikTok</fullName>
                    <default>false</default>
                    <label>TikTok</label>
                </value>
                <value>
                    <fullName>BCR</fullName>
                    <default>false</default>
                    <label>BCR</label>
                </value>
                <value>
                    <fullName>Raifeissen</fullName>
                    <default>false</default>
                    <label>Raifeissen</label>
                </value>
                <value>
                    <fullName>Finants</fullName>
                    <default>false</default>
                    <label>Finants</label>
                </value>
                <value>
                    <fullName>Goldring</fullName>
                    <default>false</default>
                    <label>Goldring</label>
                </value>
                <value>
                    <fullName>Google Search</fullName>
                    <default>false</default>
                    <label>Google Search</label>
                </value>
                <value>
                    <fullName>Perfomance Max</fullName>
                    <default>false</default>
                    <label>Perfomance Max</label>
                </value>
                <value>
                    <fullName>Discovery</fullName>
                    <default>false</default>
                    <label>Discovery</label>
                </value>
                <value>
                    <fullName>Youtube</fullName>
                    <default>false</default>
                    <label>Youtube</label>
                </value>
                <value>
                    <fullName>App</fullName>
                    <default>false</default>
                    <label>App</label>
                </value>
                <value>
                    <fullName>Display</fullName>
                    <default>false</default>
                    <label>Display</label>
                </value>
                <value>
                    <fullName>Paid referral</fullName>
                    <default>false</default>
                    <label>Paid referral</label>
                </value>
                <value>
                    <fullName>Internal Referral</fullName>
                    <default>false</default>
                    <label>Internal Referral</label>
                </value>
                <value>
                    <fullName>Sales personalized link</fullName>
                    <default>false</default>
                    <label>Sales personalized link</label>
                </value>
                <value>
                    <fullName>Yahoo</fullName>
                    <default>false</default>
                    <label>Yahoo</label>
                </value>
                <value>
                    <fullName>Taboola Ads</fullName>
                    <default>false</default>
                    <label>Taboola Ads</label>
                </value>
                <value>
                    <fullName>Adform</fullName>
                    <default>false</default>
                    <label>Adform</label>
                </value>
                <value>
                    <fullName>Blog</fullName>
                    <default>false</default>
                    <label>Blog</label>
                </value>
                <value>
                    <fullName>Biztro</fullName>
                    <default>false</default>
                    <label>Biztro</label>
                </value>
                <value>
                    <fullName>Manual input</fullName>
                    <default>false</default>
                    <label>Manual input</label>
                </value>
                <value>
                    <fullName>Outbound</fullName>
                    <default>false</default>
                    <label>Outbound</label>
                </value>
                <value>
                    <fullName>Organic</fullName>
                    <default>false</default>
                    <label>Organic</label>
                </value>
                <value>
                    <fullName>Other</fullName>
                    <default>false</default>
                    <label>Other</label>
                </value>
                <value>
                    <fullName>Webform</fullName>
                    <default>false</default>
                    <label>Webform</label>
                </value>
            </valueSetDefinition>
            <valueSettings>
                <controllingFieldValue>Lead provider</controllingFieldValue>
                <valueName>AdStrategy</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Lead provider</controllingFieldValue>
                <valueName>Companeo</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Lead provider</controllingFieldValue>
                <valueName>etc</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Marketing campaigns (precise)</controllingFieldValue>
                <controllingFieldValue>Chat</controllingFieldValue>
                <controllingFieldValue>Case</controllingFieldValue>
                <controllingFieldValue>EFG</controllingFieldValue>
                <controllingFieldValue>CPL Provider</controllingFieldValue>
                <controllingFieldValue>Public tender</controllingFieldValue>
                <controllingFieldValue>E-mailing</controllingFieldValue>
                <controllingFieldValue>Direct Mail</controllingFieldValue>
                <controllingFieldValue>Fax Mailing</controllingFieldValue>
                <controllingFieldValue>Incoming call</controllingFieldValue>
                <controllingFieldValue>Other</controllingFieldValue>
                <controllingFieldValue>MQL (Marketing Qualified Lead)</controllingFieldValue>
                <controllingFieldValue>Trade Fairs</controllingFieldValue>
                <controllingFieldValue>Phone</controllingFieldValue>
                <controllingFieldValue>Agency</controllingFieldValue>
                <controllingFieldValue>Direct order without contract</controllingFieldValue>
                <valueName>Other</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Content marketing</controllingFieldValue>
                <valueName>Blog</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Content marketing</controllingFieldValue>
                <valueName>Biztro</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Social</controllingFieldValue>
                <valueName>Meta</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Social</controllingFieldValue>
                <valueName>Linkedin</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Social</controllingFieldValue>
                <valueName>TikTok</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Contracting webform</controllingFieldValue>
                <controllingFieldValue>Webform</controllingFieldValue>
                <valueName>Webform</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>External provider</controllingFieldValue>
                <valueName>BCR</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>External provider</controllingFieldValue>
                <valueName>Raifeissen</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>External provider</controllingFieldValue>
                <valueName>Finants</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>External provider</controllingFieldValue>
                <valueName>Goldring</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Adwords</controllingFieldValue>
                <valueName>Google Search</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Adwords</controllingFieldValue>
                <valueName>Perfomance Max</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Adwords</controllingFieldValue>
                <valueName>Discovery</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Adwords</controllingFieldValue>
                <valueName>Youtube</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Adwords</controllingFieldValue>
                <valueName>App</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Adwords</controllingFieldValue>
                <valueName>Display</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Referral Programm</controllingFieldValue>
                <valueName>Paid referral</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Referral Programm</controllingFieldValue>
                <valueName>Internal Referral</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Referral Programm</controllingFieldValue>
                <valueName>Sales personalized link</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Manual Input</controllingFieldValue>
                <valueName>Manual input</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SEO</controllingFieldValue>
                <valueName>Organic</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Import</controllingFieldValue>
                <valueName>Outbound</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Email</controllingFieldValue>
                <valueName>Newsletter</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Display</controllingFieldValue>
                <valueName>Yahoo</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Display</controllingFieldValue>
                <valueName>Taboola Ads</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Display</controllingFieldValue>
                <valueName>Adform</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Case__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Case</label>
        <referenceTo>Case</referenceTo>
        <relationshipLabel>Leads</relationshipLabel>
        <relationshipName>Leads</relationshipName>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>ER_Legal_City__c</fullName>
        <externalId>false</externalId>
        <label>Legal City</label>
        <length>40</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Legal_Country_Code__c</fullName>
        <defaultValue>$User.ER_BUText__c</defaultValue>
        <externalId>false</externalId>
        <label>Legal Country Code</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetName>ER_Country_Code</valueSetName>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Legal_Country__c</fullName>
        <externalId>false</externalId>
        <label>Legal Country</label>
        <length>80</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Legal_Status__c</fullName>
        <externalId>false</externalId>
        <label>Legal Status</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Active</fullName>
                    <default>false</default>
                    <label>Active</label>
                </value>
                <value>
                    <fullName>Closed</fullName>
                    <default>false</default>
                    <label>Closed</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Legal_Street__c</fullName>
        <externalId>false</externalId>
        <label>Legal Street</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Legal_Zip_Code__c</fullName>
        <externalId>false</externalId>
        <label>Legal Zip Code</label>
        <length>20</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_MCC_Authorization__c</fullName>
        <externalId>false</externalId>
        <label>MCC Authorization</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_MCC_Clearing__c</fullName>
        <externalId>false</externalId>
        <label>MCC Clearing</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_MID_AID__c</fullName>
        <caseSensitive>false</caseSensitive>
        <externalId>false</externalId>
        <label>MID_AID</label>
        <length>80</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>true</unique>
    </fields>
    <fields>
        <fullName>ER_MID_Authorization__c</fullName>
        <externalId>false</externalId>
        <label>MID Authorization</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_MID_Clearing__c</fullName>
        <externalId>false</externalId>
        <label>MID Clearing</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_MID__c</fullName>
        <externalId>false</externalId>
        <label>MID</label>
        <length>40</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Not_Subject_to_VAT__c</fullName>
        <defaultValue>false</defaultValue>
        <description>For customers that are not subject to VAT.
Used in a Validation Rule on Lead and Account.</description>
        <externalId>false</externalId>
        <label>Not Subject to VAT</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Number_Of_Beneficiaries__c</fullName>
        <externalId>false</externalId>
        <label>Number Of Beneficiaries</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Number_of_Transactions__c</fullName>
        <externalId>false</externalId>
        <label>Number of Transactions</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Number_Of_Vehicles__c</fullName>
        <externalId>false</externalId>
        <label>Number Of Vehicles</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Number_of_outlets__c</fullName>
        <externalId>false</externalId>
        <label>Number of stores</label>
        <precision>18</precision>
        <required>false</required>
        <scale>0</scale>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Number</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Organization_ID__c</fullName>
        <externalId>false</externalId>
        <label>Organization ID</label>
        <length>40</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Other_Fiscal_ID__c</fullName>
        <externalId>false</externalId>
        <label>Other Fiscal ID</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Product_Family_Interest__c</fullName>
        <externalId>false</externalId>
        <label>Solution family interest</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>ER_BUPicklist__c</controllingField>
            <restricted>true</restricted>
            <valueSetName>ER_Product_Family</valueSetName>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <controllingFieldValue>FI</controllingFieldValue>
                <controllingFieldValue>FR</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERPL_Gift</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERPL_Sport and Culture</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERPL_TR</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERPL_Incentive</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>ERPL_Spendeo</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <controllingFieldValue>FR</controllingFieldValue>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <controllingFieldValue>LU</controllingFieldValue>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>ER_Ticket_Restaurant_Food</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <controllingFieldValue>ES</controllingFieldValue>
                <controllingFieldValue>CZ</controllingFieldValue>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <controllingFieldValue>LU</controllingFieldValue>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>ER_Employee_benefits_quality_of_life</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <valueName>ER_Fleet</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <controllingFieldValue>ES</controllingFieldValue>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <valueName>ER_Mobility</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <controllingFieldValue>ES</controllingFieldValue>
                <controllingFieldValue>CZ</controllingFieldValue>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>ER_Incentives &amp; rewards</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <controllingFieldValue>ES</controllingFieldValue>
                <controllingFieldValue>CZ</controllingFieldValue>
                <valueName>ER_Public Social Programs</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <controllingFieldValue>ES</controllingFieldValue>
                <controllingFieldValue>CZ</controllingFieldValue>
                <valueName>ER_Ticket_Restaurant_Meal</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Card cadou Edenred</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Card Cadou Edenred - deprecated</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Card cultural Edenred</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Card de masă Edenred</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Card de vacanţă Edenred</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Cresa</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Social Alimente</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Social Asist</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Social Card Mese Calde</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Social Gradinita</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Social Nou Nascuti</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Sprijin Educational Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Suport Cazare</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Suport Mâncare</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Suport Medicamente</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Suport Transport</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Edenred Suport Uz personal</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Prima Didactica Dezvoltare</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Prima Didactica Uz Personal</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Tichet Cultural</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Ticket Asist</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Ticket Cadou</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Ticket Cresa</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Ticket Junior</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Ticket Restaurant</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>RO</controllingFieldValue>
                <valueName>Ticket Vacanta</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>SK</controllingFieldValue>
                <valueName>ER_Corporate Payment</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>CZ</controllingFieldValue>
                <valueName>ER_Public Social Programs</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>HU</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERHU_Edenred_Gift_Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>HU</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERHU_Edenred_Schooling_Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>HU</controllingFieldValue>
                <controllingFieldValue>CEN</controllingFieldValue>
                <valueName>ERHU_Edenred_Culture_&amp;_Sport_Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <valueName>ERPL_Ticket Holiday</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_ProviderDescription__c</fullName>
        <description>Credit Scoring Provider Description</description>
        <externalId>false</externalId>
        <label>Provider Description</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>TextArea</type>
    </fields>
    <fields>
        <fullName>ER_Role__c</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <externalId>false</externalId>
        <label>Role</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>ER_BUPicklist__c</controllingField>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Owner</fullName>
                    <default>false</default>
                    <label>Owner</label>
                </value>
                <value>
                    <fullName>Assistant</fullName>
                    <default>false</default>
                    <label>Assistant</label>
                </value>
                <value>
                    <fullName>Other</fullName>
                    <default>false</default>
                    <label>Other</label>
                </value>
                <value>
                    <fullName>Manager</fullName>
                    <default>false</default>
                    <label>Manager</label>
                </value>
                <value>
                    <fullName>Accountant</fullName>
                    <default>false</default>
                    <label>Accountant</label>
                </value>
                <value>
                    <fullName>Purchasing Manager</fullName>
                    <default>false</default>
                    <label>Purchasing Manager</label>
                </value>
                <value>
                    <fullName>Administrator</fullName>
                    <default>false</default>
                    <label>Administrator</label>
                </value>
                <value>
                    <fullName>Management assistant</fullName>
                    <default>false</default>
                    <label>Management assistant</label>
                </value>
                <value>
                    <fullName>Administrative employee</fullName>
                    <default>false</default>
                    <label>Administrative employee</label>
                </value>
                <value>
                    <fullName>HR Director</fullName>
                    <default>false</default>
                    <label>HR Director</label>
                </value>
                <value>
                    <fullName>CFO</fullName>
                    <default>false</default>
                    <label>CFO</label>
                </value>
                <value>
                    <fullName>Treasurer</fullName>
                    <default>false</default>
                    <label>Treasurer</label>
                </value>
                <value>
                    <fullName>Independant administrator</fullName>
                    <default>false</default>
                    <label>Independant administrator</label>
                </value>
                <value>
                    <fullName>Sales representative</fullName>
                    <default>false</default>
                    <label>Sales representative</label>
                </value>
                <value>
                    <fullName>Policy officer</fullName>
                    <default>false</default>
                    <label>Policy officer</label>
                </value>
                <value>
                    <fullName>Product manager</fullName>
                    <default>false</default>
                    <label>Product manager</label>
                </value>
                <value>
                    <fullName>Project manager</fullName>
                    <default>false</default>
                    <label>Project manager</label>
                </value>
                <value>
                    <fullName>Head of Department</fullName>
                    <default>false</default>
                    <label>Head of Department</label>
                </value>
                <value>
                    <fullName>Head of school</fullName>
                    <default>false</default>
                    <label>Head of school</label>
                </value>
                <value>
                    <fullName>Comptable</fullName>
                    <default>false</default>
                    <label>Comptable</label>
                </value>
                <value>
                    <fullName>Departmental councillor</fullName>
                    <default>false</default>
                    <label>Departmental councillor</label>
                </value>
                <value>
                    <fullName>Municipal council</fullName>
                    <default>false</default>
                    <label>Municipal council</label>
                </value>
                <value>
                    <fullName>Regional Councillor</fullName>
                    <default>false</default>
                    <label>Regional Councillor</label>
                </value>
                <value>
                    <fullName>DGA</fullName>
                    <default>false</default>
                    <label>DGA</label>
                </value>
                <value>
                    <fullName>DGS</fullName>
                    <default>false</default>
                    <label>DGS</label>
                </value>
                <value>
                    <fullName>Director</fullName>
                    <default>false</default>
                    <label>Director</label>
                </value>
                <value>
                    <fullName>Deputy director</fullName>
                    <default>false</default>
                    <label>Deputy director</label>
                </value>
                <value>
                    <fullName>Elected representative</fullName>
                    <default>false</default>
                    <label>Elected representative</label>
                </value>
                <value>
                    <fullName>Mayor</fullName>
                    <default>false</default>
                    <label>Mayor</label>
                </value>
                <value>
                    <fullName>Deputy mayor</fullName>
                    <default>false</default>
                    <label>Deputy mayor</label>
                </value>
                <value>
                    <fullName>Chairman</fullName>
                    <default>false</default>
                    <label>Chairman</label>
                </value>
                <value>
                    <fullName>Liberal profession</fullName>
                    <default>false</default>
                    <label>Liberal profession</label>
                </value>
                <value>
                    <fullName>Person in charge</fullName>
                    <default>false</default>
                    <label>Person in charge</label>
                </value>
                <value>
                    <fullName>Sales Manager</fullName>
                    <default>false</default>
                    <label>Sales Manager</label>
                </value>
                <value>
                    <fullName>Secretary</fullName>
                    <default>false</default>
                    <label>Secretary</label>
                </value>
                <value>
                    <fullName>Chief administrative</fullName>
                    <default>false</default>
                    <label>Chief administrative</label>
                </value>
                <value>
                    <fullName>Vice-president</fullName>
                    <default>false</default>
                    <label>Vice-president</label>
                </value>
                <value>
                    <fullName>Decision Maker</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Decision Maker</label>
                </value>
                <value>
                    <fullName>Decision Preparator</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Decision Preparator</label>
                </value>
                <value>
                    <fullName>Middle Management</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Middle Management</label>
                </value>
                <value>
                    <fullName>Top Management</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Top Management</label>
                </value>
                <value>
                    <fullName>Vice-Président</fullName>
                    <default>false</default>
                    <isActive>false</isActive>
                    <label>Vice-Président</label>
                </value>
            </valueSetDefinition>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Administrative employee</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Administrator</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Assistant</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Other</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Manager</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Accountant</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Treasurer</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Sales representative</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Policy officer</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Product manager</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Project manager</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Head of Department</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Head of school</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Comptable</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Departmental councillor</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Municipal council</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Regional Councillor</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>DGA</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>DGS</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Director</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Deputy director</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Elected representative</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Mayor</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Deputy mayor</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Chairman</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Liberal profession</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Person in charge</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Sales Manager</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Secretary</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Chief administrative</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Vice-president</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_SmartER_Context__c</fullName>
        <defaultValue>false</defaultValue>
        <description>If the checkbox is true, the Lead record has been created through SmartER context</description>
        <externalId>false</externalId>
        <label>SmartER Context</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_SmartER_Drop_Case_Step__c</fullName>
        <externalId>false</externalId>
        <label>SmartER Drop Case Step</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Contact</fullName>
                    <default>false</default>
                    <label>Contact</label>
                </value>
                <value>
                    <fullName>Company</fullName>
                    <default>false</default>
                    <label>Company</label>
                </value>
                <value>
                    <fullName>Account</fullName>
                    <default>false</default>
                    <label>Account</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_SmartER_Original_Lead_Id__c</fullName>
        <externalId>false</externalId>
        <formula>CASESAFEID(Id)</formula>
        <label>SmartER Original Lead Id</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Solution__c</fullName>
        <externalId>false</externalId>
        <label>Solution</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>ER_BUPicklist__c</controllingField>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>ERHU_Edenred_Gift_Card</fullName>
                    <default>false</default>
                    <label>Edenred Gift Card</label>
                </value>
                <value>
                    <fullName>ERHU_Edenred_Schooling_Card</fullName>
                    <default>false</default>
                    <label>Edenred Schooling Card</label>
                </value>
                <value>
                    <fullName>ERHU_Edenred_Culture_&amp;_Sport_Card</fullName>
                    <default>false</default>
                    <label>Edenred Culture &amp; Sport Card</label>
                </value>
                <value>
                    <fullName>ERPL_Gift</fullName>
                    <default>false</default>
                    <label>Gift</label>
                </value>
                <value>
                    <fullName>ERPL_Sport and Culture</fullName>
                    <default>false</default>
                    <label>Sport and Culture</label>
                </value>
                <value>
                    <fullName>ERPL_TR</fullName>
                    <default>false</default>
                    <label>TR</label>
                </value>
                <value>
                    <fullName>ERPL_Incentive</fullName>
                    <default>false</default>
                    <label>Incentive</label>
                </value>
                <value>
                    <fullName>ERPL_Spendeo</fullName>
                    <default>false</default>
                    <label>Spendeo</label>
                </value>
            </valueSetDefinition>
            <valueSettings>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <valueName>ERHU_Edenred_Gift_Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <valueName>ERHU_Edenred_Schooling_Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>BE</controllingFieldValue>
                <controllingFieldValue>BENELUX</controllingFieldValue>
                <valueName>ERHU_Edenred_Culture_&amp;_Sport_Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <valueName>ERPL_Gift</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <valueName>ERPL_Sport and Culture</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <valueName>ERPL_TR</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <valueName>ERPL_Incentive</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>PL</controllingFieldValue>
                <valueName>ERPL_Spendeo</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Store_Name__c</fullName>
        <externalId>false</externalId>
        <label>Store Name</label>
        <length>80</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Sub_MID_Authorization__c</fullName>
        <externalId>false</externalId>
        <label>Sub MID Authorization</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Sub_MID_Clearing__c</fullName>
        <externalId>false</externalId>
        <label>Sub MID Clearing</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Sync_to_HubSpot__c</fullName>
        <externalId>false</externalId>
        <label>Sync to HubSpot</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>No</fullName>
                    <default>true</default>
                    <label>No</label>
                </value>
                <value>
                    <fullName>Yes</fullName>
                    <default>false</default>
                    <label>Yes</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_TID_Authorization__c</fullName>
        <externalId>false</externalId>
        <label>TID Authorization</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_TID_Clearing__c</fullName>
        <externalId>false</externalId>
        <label>TID Clearing</label>
        <length>18</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Tax_Office__c</fullName>
        <externalId>false</externalId>
        <label>Tax Office</label>
        <length>50</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Terminal__c</fullName>
        <externalId>false</externalId>
        <label>Terminal</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Yes</fullName>
                    <default>false</default>
                    <label>Yes</label>
                </value>
                <value>
                    <fullName>No</fullName>
                    <default>false</default>
                    <label>No</label>
                </value>
                <value>
                    <fullName>Don&apos;t know</fullName>
                    <default>true</default>
                    <label>Don&apos;t know</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_To_Be_Converted__c</fullName>
        <defaultValue>false</defaultValue>
        <description>This field is used by the API smarter in order to convert leads</description>
        <externalId>false</externalId>
        <label>To Be Converted</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_UTM_tag__c</fullName>
        <externalId>false</externalId>
        <label>UTM tag</label>
        <length>1500</length>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <fields>
        <fullName>ER_VAT_Number__c</fullName>
        <externalId>false</externalId>
        <label>VAT Number</label>
        <length>40</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Enterprise_Number__c</fullName>
        <externalId>false</externalId>
        <label>Enterprise Number</label>
        <length>60</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_High_level_activity__c</fullName>
        <externalId>false</externalId>
        <label>High level activity</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Restaurant &amp; Food</fullName>
                    <default>false</default>
                    <label>Restaurant &amp; Food</label>
                </value>
                <value>
                    <fullName>Retail</fullName>
                    <default>false</default>
                    <label>Retail</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Industry__c</fullName>
        <externalId>false</externalId>
        <label>Industry</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>ER_BUPicklist__c</controllingField>
            <restricted>true</restricted>
            <valueSetName>ER_Industry</valueSetName>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Association</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Committee of social affairs</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Communications agency</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Council house</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Departmental council</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Franchise</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Government</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Government VIP</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Independent</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Intercommunal cooperation services</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Ministry</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Mixed-ownership company</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Non-profit organization</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Private company</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Public company</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Public establishment</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Regional council</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Social institution</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>FR</controllingFieldValue>
                <valueName>Works concil</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Agriculture</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Apparel</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Automotive</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Call Center &amp; Telecommunications</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Chemicals &amp; Plastic</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Construction</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Care Services</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Education</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Electronics</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Energy</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Engineering</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Entertainment</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Environmental</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Fabrics &amp; Linen items</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Financial and Banking services</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Food &amp; Beverage</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Furniture</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Gambling</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Gas Station</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Hospitality</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Insurance</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Machinery</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Manufacturing</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Media</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Mining</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Not For Profit</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Other</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Pharmaceuticals</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Publishing &amp; Paper</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Real estate</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Recreation</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Retail</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Security</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Shipping</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Sport</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Technology &amp; Computer Science</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Tourism</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Transportation &amp; Logistics</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Utilities</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Wholesale Trade</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Work Agency</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Facilities</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>GR</controllingFieldValue>
                <valueName>Healthcare</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Language__c</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <defaultValue>$User.ER_BUText__c</defaultValue>
        <externalId>false</externalId>
        <label>Language</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetName>ER_Language</valueSetName>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Legal_Form__c</fullName>
        <externalId>false</externalId>
        <label>Legal Form</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Legal_Name__c</fullName>
        <externalId>false</externalId>
        <label>Legal Name</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Legal_State_Province__c</fullName>
        <externalId>false</externalId>
        <label>Legal State/Province</label>
        <length>80</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Email</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Fax</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>HasOptedOutOfEmail</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>HasOptedOutOfFax</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Industry</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
    </fields>
    <fields>
        <fullName>Jigsaw</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>LastTransferDate</fullName>
    </fields>
    <fields>
        <fullName>LeadSource</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
    </fields>
    <fields>
        <fullName>MobilePhone</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Name</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>true</trackFeedHistory>
        <trackHistory>true</trackHistory>
    </fields>
    <fields>
        <fullName>NumberOfEmployees</fullName>
        <complianceGroup>GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>true</trackHistory>
    </fields>
    <fields>
        <fullName>OwnerId</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>true</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Phone</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Rating</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
    </fields>
    <fields>
        <fullName>Status</fullName>
        <trackFeedHistory>true</trackFeedHistory>
        <trackHistory>true</trackHistory>
        <type>Picklist</type>
    </fields>
    <fields>
        <fullName>Title</fullName>
        <complianceGroup>PII;GDPR</complianceGroup>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>Website</fullName>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
    </fields>
    <fields>
        <fullName>ER_Lead_Requestor__c</fullName>
        <externalId>false</externalId>
        <inlineHelpText>Please fill the field in with email address(es)</inlineHelpText>
        <label>Lead Requestor</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>TextArea</type>
    </fields>
    <fields>
        <fullName>ER_NaceList__c</fullName>
        <description>NACE list separated by semicolumn</description>
        <externalId>false</externalId>
        <label>Nace List</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>TextArea</type>
    </fields>
    <fields>
        <fullName>ER_NaceNameList__c</fullName>
        <externalId>false</externalId>
        <label>Nace Name List</label>
        <length>131072</length>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <fields>
        <fullName>ER_Registration_Number__c</fullName>
        <externalId>false</externalId>
        <label>Company Registration Number</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Rejected_Reason__c</fullName>
        <externalId>false</externalId>
        <label>Rejected Reason</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <controllingField>Status</controllingField>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>ERRO_Merchant</fullName>
                    <default>false</default>
                    <label>Merchant</label>
                </value>
                <value>
                    <fullName>ERRO_User</fullName>
                    <default>false</default>
                    <label>User</label>
                </value>
                <value>
                    <fullName>ERRO_Existing Client</fullName>
                    <default>false</default>
                    <label>Existing Client</label>
                </value>
                <value>
                    <fullName>Duplicate Lead</fullName>
                    <default>false</default>
                    <label>Duplicate Lead</label>
                </value>
                <value>
                    <fullName>Spam</fullName>
                    <default>false</default>
                    <label>Spam</label>
                </value>
                <value>
                    <fullName>ERRO_Other product than Meal Card</fullName>
                    <default>false</default>
                    <label>Other product than Meal Card</label>
                </value>
                <value>
                    <fullName>ERRO_Other Reason for Non Workable</fullName>
                    <default>false</default>
                    <label>Other Reason for Non Workable</label>
                </value>
                <value>
                    <fullName>No interest</fullName>
                    <default>false</default>
                    <label>No interest</label>
                </value>
                <value>
                    <fullName>No budget</fullName>
                    <default>false</default>
                    <label>No budget</label>
                </value>
                <value>
                    <fullName>No Terminal</fullName>
                    <default>false</default>
                    <label>No Terminal</label>
                </value>
                <value>
                    <fullName>Acceptor added/updated</fullName>
                    <default>false</default>
                    <label>Acceptor added/updated</label>
                </value>
                <value>
                    <fullName>Already Merchant</fullName>
                    <default>false</default>
                    <label>Already Merchant</label>
                </value>
                <value>
                    <fullName>Company does not exist / Out of business</fullName>
                    <default>false</default>
                    <label>Company does not exist / Out of business</label>
                </value>
                <value>
                    <fullName>Inability to contact</fullName>
                    <default>false</default>
                    <label>Inability to contact</label>
                </value>
                <value>
                    <fullName>Unreachable after call back actions</fullName>
                    <default>false</default>
                    <label>Unreachable after call back actions</label>
                </value>
                <value>
                    <fullName>Missing Key Information</fullName>
                    <default>false</default>
                    <label>Missing Key Information</label>
                </value>
                <value>
                    <fullName>Out of Benefit Target</fullName>
                    <default>false</default>
                    <label>Out of Benefit Target</label>
                </value>
                <value>
                    <fullName>Employee Request</fullName>
                    <default>false</default>
                    <label>Employee Request</label>
                </value>
                <value>
                    <fullName>Not a Lead</fullName>
                    <default>false</default>
                    <label>Not a Lead</label>
                </value>
            </valueSetDefinition>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>ERRO_Merchant</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>ERRO_User</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>ERRO_Existing Client</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Duplicate Lead</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Spam</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>ERRO_Other product than Meal Card</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>ERRO_Other Reason for Non Workable</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>No interest</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>No budget</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>No Terminal</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Acceptor added/updated</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Already Merchant</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Company does not exist / Out of business</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Inability to contact</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Unreachable after call back actions</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Missing Key Information</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Out of Benefit Target</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Employee Request</valueName>
            </valueSettings>
            <valueSettings>
                <controllingFieldValue>Rejected</controllingFieldValue>
                <valueName>Not a Lead</valueName>
            </valueSettings>
        </valueSet>
    </fields>
    <fields>
        <fullName>ER_Initial_Product_Interest_External_ID__c</fullName>
        <externalId>false</externalId>
        <label>Initial Product Of Interest External ID</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_No_Competitor__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>No Competitor</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_dd_Trace_Id__c</fullName>
        <externalId>false</externalId>
        <label>dd Trace Id</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ER_Commercial_Consent__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <inlineHelpText>I accept to receive commercial information from Edenred</inlineHelpText>
        <label>Commercial Consent</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Relational_Communication__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <inlineHelpText>I agree to be informed about new features or services…</inlineHelpText>
        <label>Relational Communication</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ER_Inquiries__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>Inquiries</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
	<fields>
		<fullName>ER_OVT_Code__c</fullName>
		<externalId>false</externalId>
		<label>OVT Code</label>
		<length>255</length>
		<required>false</required>
		<trackFeedHistory>false</trackFeedHistory>
		<trackHistory>false</trackHistory>
		<type>Text</type>
		<unique>false</unique>
	</fields>
	<fields>
		<fullName>ER_OVT_Operator__c</fullName>
		<externalId>false</externalId>
		<label>OVT Operator</label>
		<length>255</length>
		<required>false</required>
		<trackFeedHistory>false</trackFeedHistory>
		<trackHistory>false</trackHistory>
		<type>Text</type>
		<unique>false</unique>
	</fields>
    <fields>
        <fullName>ER_Campaign_Direction__c</fullName>
        <externalId>false</externalId>
        <formula>if(ISPICKVAL(LeadSource , &apos;Import&apos;) ,&apos;Outbound&apos;,&apos;Inbound&apos;)</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Campaign Direction</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_County__c</fullName>
        <externalId>false</externalId>
        <label>County</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Declared_Lead_Type__c</fullName>
        <externalId>false</externalId>
        <label>Declared Lead Type</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Picklist</type>
        <valueSet>
            <restricted>true</restricted>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>User</fullName>
                    <default>false</default>
                    <label>User</label>
                </value>
                <value>
                    <fullName>Existing client</fullName>
                    <default>false</default>
                    <label>Existing client</label>
                </value>
                <value>
                    <fullName>Merchant</fullName>
                    <default>false</default>
                    <label>Merchant</label>
                </value>
                <value>
                    <fullName>Prospect client</fullName>
                    <default>false</default>
                    <label>Prospect client</label>
                </value>
                <value>
                    <fullName>Other</fullName>
                    <default>false</default>
                    <label>Other</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>ERRO_Employee_Size_Range__c</fullName>
        <externalId>false</externalId>
        <formula>IF(
AND(NumberOfEmployees &gt;= 1,NumberOfEmployees &lt;= 5), &quot;[1-5]&quot;,
IF(
AND(NumberOfEmployees &gt;= 6,NumberOfEmployees &lt;= 10), &quot;[6-10]&quot;,
IF(
AND(NumberOfEmployees &gt;= 11,NumberOfEmployees &lt;= 20), &quot;[11-20]&quot;,
IF(
AND(NumberOfEmployees &gt;= 21,NumberOfEmployees &lt;= 45), &quot;[21-45]&quot;,
IF(
AND(NumberOfEmployees &gt;= 46,NumberOfEmployees &lt;= 70), &quot;[46-70]&quot;,
IF(
AND(NumberOfEmployees &gt;= 71,NumberOfEmployees &lt;= 200), &quot;[71-200]&quot;,
IF(
AND(NumberOfEmployees &gt;= 201,NumberOfEmployees &lt;= 500), &quot;[201-500]&quot;,

IF(NumberOfEmployees &gt;500, &quot;[500+]&quot;, &quot; &quot;))))))))</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
        <label>Employee Size Range</label>
        <required>false</required>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_GDPR__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <label>GDPR</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <fields>
        <fullName>ERRO_Google_Ad_Click_Id__c</fullName>
        <externalId>false</externalId>
        <label>Google Ad Click Id</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Hubspot_Source__c</fullName>
        <externalId>false</externalId>
        <label>Hubspot Source</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Last_Touch_Converting_Campaign__c</fullName>
        <externalId>false</externalId>
        <label>Last Touch Converting Campaign</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Latest_Source_Drill_Down_1__c</fullName>
        <externalId>false</externalId>
        <label>Latest Source Drill-Down 1</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Latest_Source_Drill_Down_2__c</fullName>
        <externalId>false</externalId>
        <label>Latest Source Drill-Down 2</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Page_URL__c</fullName>
        <externalId>false</externalId>
        <label>Page URL</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Recent_Conversion_Date__c</fullName>
        <externalId>false</externalId>
        <label>Recent Conversion Date</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Date</type>
    </fields>
    <fields>
        <fullName>ERRO_Referral_Contact_person__c</fullName>
        <externalId>false</externalId>
        <label>Referral Contact person (Name &amp; Surname)</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Referral_Email__c</fullName>
        <externalId>false</externalId>
        <label>Referral Email</label>
        <length>255</length>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>ERRO_Referral_Phone_number__c</fullName>
        <externalId>false</externalId>
        <label>Referral Phone number</label>
        <required>false</required>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Phone</type>
    </fields>
    <fields>
        <fullName>ERRO_Terms_And_Condition__c</fullName>
        <defaultValue>false</defaultValue>
        <externalId>false</externalId>
        <inlineHelpText>Terms and Condition are met</inlineHelpText>
        <label>Terms And Condition</label>
        <trackFeedHistory>false</trackFeedHistory>
        <trackHistory>false</trackHistory>
        <type>Checkbox</type>
    </fields>
    <listViews>
        <fullName>ERGR_All_Open_Client_Leads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>LEAD.STATUS</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <columns>CORE.USERS.LAST_NAME</columns>
        <filterScope>Everything</filterScope>
        <filters>
            <field>LEAD.LEAD_SOURCE</field>
            <operation>equals</operation>
            <value>Unmatched MCC</value>
        </filters>
        <filters>
            <field>LEAD.RECORDTYPE</field>
            <operation>equals</operation>
            <value>Lead.ER_Client_Lead_RT</value>
        </filters>
        <label>All Open Client Leads</label>
        <sharedTo>
            <roleAndSubordinates>ERGR_Field_Client_LF</roleAndSubordinates>
            <roleAndSubordinates>ERGR_Field_Client_N</roleAndSubordinates>
            <roleAndSubordinates>ERGR_Field_Client_S</roleAndSubordinates>
            <roleAndSubordinates>ERGR_Field_North</roleAndSubordinates>
            <roleAndSubordinates>ERGR_KAM</roleAndSubordinates>
            <roleAndSubordinates>ERGR_KAM_Client</roleAndSubordinates>
            <roleAndSubordinates>ERGR_MM</roleAndSubordinates>
            <roleAndSubordinates>ERGR_Sales</roleAndSubordinates>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>AllOpenLeads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>LEAD.RECORDTYPE</columns>
        <columns>ER_Product_Family_Interest__c</columns>
        <columns>LEAD.LEAD_SOURCE</columns>
        <columns>LEAD.STATE</columns>
        <columns>LEAD.PHONE</columns>
        <columns>LEAD.MOBILE_PHONE</columns>
        <columns>LEAD.EMAIL</columns>
        <columns>LEAD.STATUS</columns>
        <columns>CORE.USERS.FIRST_NAME</columns>
        <columns>CORE.USERS.LAST_NAME</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <filterScope>Everything</filterScope>
        <filters>
            <field>LEAD.STATUS</field>
            <operation>notEqual</operation>
            <value>Qualified</value>
        </filters>
        <label>All Open Leads</label>
    </listViews>
    <listViews>
        <fullName>ERRO_HS_LEAD_Lead</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>ER_Rejected_Reason__c</columns>
        <columns>LEAD.EMAIL</columns>
        <columns>LEAD.STATUS</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <columns>CORE.USERS.ALIAS</columns>
        <columns>LEAD.UNREAD</columns>
        <filterScope>Queue</filterScope>
        <label>HS LEAD</label>
        <queue>ERRO_HS_LEAD</queue>
        <sharedTo>
            <allInternalUsers></allInternalUsers>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>ER_HomePage_List_of_Leads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>ERBE_Initial_Product_Of_Interest__c</columns>
        <columns>ER_Product_Family_Interest__c</columns>
        <columns>LEAD.RATING</columns>
        <columns>CORE.USERS.ALIAS</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <filterScope>Everything</filterScope>
        <filters>
            <field>LEAD.UNREAD</field>
            <operation>equals</operation>
            <value>1</value>
        </filters>
        <label>Open Leads to be contacted</label>
    </listViews>
    <listViews>
        <fullName>MyUnreadLeads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>LEAD.RECORDTYPE</columns>
        <columns>ER_Product_Family_Interest__c</columns>
        <columns>LEAD.LEAD_SOURCE</columns>
        <columns>LEAD.STATE</columns>
        <columns>LEAD.PHONE</columns>
        <columns>LEAD.MOBILE_PHONE</columns>
        <columns>LEAD.EMAIL</columns>
        <columns>LEAD.RATING</columns>
        <columns>LEAD.UNREAD</columns>
        <columns>LEAD.STATUS</columns>
        <columns>CORE.USERS.LAST_NAME</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <filterScope>Mine</filterScope>
        <filters>
            <field>LEAD.UNREAD</field>
            <operation>equals</operation>
            <value>1</value>
        </filters>
        <label>My Unread Leads</label>
    </listViews>
     <listViews>
        <fullName>My_Leads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>LEAD.RECORDTYPE</columns>
        <columns>ER_Product_Family_Interest__c</columns>
        <columns>LEAD.LEAD_SOURCE</columns>
        <columns>LEAD.STATE</columns>
        <columns>LEAD.PHONE</columns>
        <columns>LEAD.MOBILE_PHONE</columns>
        <columns>LEAD.EMAIL</columns>
        <columns>LEAD.STATUS</columns>
        <columns>LEAD.UNREAD</columns>
        <columns>CORE.USERS.LAST_NAME</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <filterScope>Mine</filterScope>
        <label>My Leads</label>
    </listViews>
    <listViews>
        <fullName>My_Open_leads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>LEAD.CITY</columns>
        <columns>LEAD.STREET</columns>
        <columns>LEAD.RECORDTYPE</columns>
        <columns>ER_Product_Family_Interest__c</columns>
        <columns>LEAD.LEAD_SOURCE</columns>
        <columns>LEAD.STATE</columns>
        <columns>LEAD.PHONE</columns>
        <columns>LEAD.MOBILE_PHONE</columns>
        <columns>LEAD.EMAIL</columns>
        <columns>LEAD.STATUS</columns>
        <columns>LEAD.UNREAD</columns>
        <columns>CORE.USERS.LAST_NAME</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <filterScope>Mine</filterScope>
        <filters>
            <field>LEAD.STATUS</field>
            <operation>equals</operation>
            <value>New,Qualification,Qualified</value>
        </filters>
        <label>My Open leads</label>
        <sharedTo>
            <roleAndSubordinates>Czech_Republic</roleAndSubordinates>
        </sharedTo>
    </listViews>
    <listViews>
        <fullName>TodaysLeads</fullName>
        <columns>FULL_NAME</columns>
        <columns>LEAD.COMPANY</columns>
        <columns>LEAD.RECORDTYPE</columns>
        <columns>ER_Product_Family_Interest__c</columns>
        <columns>LEAD.LEAD_SOURCE</columns>
        <columns>LEAD.STATE</columns>
        <columns>LEAD.PHONE</columns>
        <columns>LEAD.MOBILE_PHONE</columns>
        <columns>LEAD.EMAIL</columns>
        <columns>LEAD.STATUS</columns>
        <columns>CORE.USERS.LAST_NAME</columns>
        <columns>LEAD.CREATED_DATE</columns>
        <columns>LEAD.UNREAD</columns>
        <filterScope>Everything</filterScope>
        <filters>
            <field>LEAD.CREATED_DATE</field>
            <operation>equals</operation>
            <value>TODAY</value>
        </filters>
        <label>Today&apos;s Leads</label>
    </listViews>
    <recordTypeTrackFeedHistory>false</recordTypeTrackFeedHistory>
    <recordTypeTrackHistory>false</recordTypeTrackHistory>
    <recordTypes>
        <fullName>ER_Client_Lead_RT</fullName>
        <active>true</active>
        <businessProcess>Client Lead Process</businessProcess>
        <label>Client Lead</label>
        <picklistValues>
            <picklist>ERCZ_Academic_Title__c</picklist>
            <values>
                <fullName>Bc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BcA%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CSc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DSc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DiS%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Dr.</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DrSc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ing%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ing%2E arch%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>JUDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MDDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MSDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MUDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MVDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MgA%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mgr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PaedDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ph%2ED%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PhDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PhMr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PharmDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RCDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RNDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RSDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RTDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Th%2ED%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ThDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ThLic%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ThMgr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>as%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>doc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>odb%2E as%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>p%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pan</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>paní</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pi</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>prof%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pí</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pí%2E</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ERRO_Declared_Lead_Type__c</picklist>
            <values>
                <fullName>Existing client</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Merchant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Prospect client</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>User</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_BUPicklist__c</picklist>
            <values>
                <fullName>AE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>AR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>AT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BENELUX</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BG</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CEN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CZ</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ES</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FI</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>GR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>HU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>IT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>JP</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LB</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MA</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MD</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MX</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MY</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PC</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SG</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SK</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>TR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>TW</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>UK</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>US</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>UTA</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VN</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_High_level_activity__c</picklist>
            <values>
                 <fullName>Restaurant &amp; Food</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Retail</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Industry__c</picklist>
            <values>
                <fullName>Agriculture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Apparel</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Association</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Automotive</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Banking</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Biotechnology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Call Center %26 Telecommunications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Care Services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chemicals</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chemicals %26 Plastic</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Committee of social affairs</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Communications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Communications agency</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Construction</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Consulting</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Council house</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Culture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Departmental council</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Education</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Electronics</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Energy</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Engineering</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Entertainment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Environmental</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Fabrics %26 Linen items</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Facilities</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Finance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Financial and Banking services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Food %26 Beverage</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Franchise</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Furniture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Gambling</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Gas Station</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Government</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Government VIP</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Healthcare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Hospitality</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Independent</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Insurance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Intercommunal cooperation services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lottery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Machinery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manufacturing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Marketing and advertising agencies</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Media</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mining</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ministry</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mixed-ownership company</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Non-profit organization</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Not For Profit</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Pharmaceuticals</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Private company</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public company</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public establishment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Publishing %26 Paper</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Real estate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Recreation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Regional council</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Retail</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Security</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Shipping</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Social institution</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Sport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Technology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Technology %26 Computer Science</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Telecommunications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Tourism</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Transportation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Transportation %26 Logistics</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Utilities</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Wholesale Trade</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Work Agency</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Works concil</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Language__c</picklist>
            <values>
                <fullName>CZ</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>EN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ES</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>HU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>NL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SK</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Lead_Channel__c</picklist>
            <values>
                <fullName>Client</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Customer</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Internal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lead provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Merchant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Trade association</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>User</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Lead_Type__c</picklist>
            <values>
                <fullName>Client</fullName>
                <default>true</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Lead_sub_source__c</picklist>
            <values>
                <fullName>Abandonned contracting</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>AdStrategy</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Adform</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Adwords%3A Google%2C Bing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>App</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BCR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Biztro</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Blog</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Companeo</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Discovery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Display</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Encore</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Finants</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Goldring</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Google Search</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Internal Referral</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Internal Webform</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lead provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Linkedin</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manual input</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Meta</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Newsletter</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Organic</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Outbound</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other adwords</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Paid referral</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Perfomance Max</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public Register - list of existing companies</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public Register - list of new created companies</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Raifeissen</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Sales personalized link</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Taboola Ads</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>TikTok</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Yahoo</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Webform</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Youtube</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>etc</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Product_Family_Interest__c</picklist>
            <values>
                <fullName>Card Cadou Edenred - deprecated</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Card cadou Edenred</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Card cultural Edenred</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Card de masă Edenred</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Card de vacanţă Edenred</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Culture_%26_Sport_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Gift_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Schooling_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Gift</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Incentive</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Spendeo</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Sport and Culture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_TR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Ticket Holiday</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Corporate Payment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Employee_benefits_quality_of_life</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Fleet</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Incentives %26 rewards</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Mobility</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Public Social Programs</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_SK_Digital_Solutions</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_SK_Ticket_Gifts</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_SK_Ticket_Services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Ticket_Restaurant_Food</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Ticket_Restaurant_Meal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Cresa</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Social Alimente</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Social Asist</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Social Card Mese Calde</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Social Gradinita</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Social Nou Nascuti</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Sprijin Educational Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Suport Cazare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Suport Medicamente</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Suport Mâncare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Suport Transport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Edenred Suport Uz personal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Prima Didactica Dezvoltare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Prima Didactica Uz Personal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Tichet Cultural</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ticket Asist</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ticket Cadou</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ticket Cresa</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ticket Junior</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ticket Restaurant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ticket Vacanta</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Legal_Status__c</picklist>
            <values>
                <fullName>Active</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Closed</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Rejected_Reason__c</picklist>
            <values>
                <fullName>Duplicate Lead</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERRO_Existing Client</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERRO_Merchant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERRO_Other Reason for Non Workable</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERRO_Other product than Meal Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERRO_User</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Employee Request</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Inability to contact</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Missing Key Information</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No budget</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No interest</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Not a Lead</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Out of Benefit Target</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Spam</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Unreachable after call back actions</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Role__c</picklist>
            <values>
                <fullName>Accountant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Administrative employee</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Administrator</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Assistant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CFO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chairman</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chief administrative</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DGA</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DGS</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Departmental councillor</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Deputy director</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Deputy mayor</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Director</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Elected representative</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>HR Director</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Head of Department</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Head of school</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Independant administrator</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Liberal profession</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Management assistant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mayor</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Municipal council</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Owner</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Person in charge</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Policy officer</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Product manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Project manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Purchasing Manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Regional Councillor</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Sales Manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Sales representative</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Secretary</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Treasurer</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Vice-president</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Solution__c</picklist>
            <values>
                <fullName>ERHU_Edenred_Culture_%26_Sport_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Gift_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Schooling_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Gift</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Incentive</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Spendeo</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Sport and Culture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_TR</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Sync_to_HubSpot__c</picklist>
            <values>
                <fullName>No</fullName>
                <default>true</default>
            </values>
            <values>
                <fullName>Yes</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Terminal__c</picklist>
            <values>
                <fullName>Don%27t know</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Yes</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Industry</picklist>
            <values>
                <fullName>Agriculture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Apparel</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Banking</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Biotechnology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chemicals</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Communications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Construction</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Consulting</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Education</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Electronics</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Energy</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Engineering</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Entertainment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Environmental</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Finance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Food %26 Beverage</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Gas Station</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Government</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Healthcare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Hospitality</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Insurance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Machinery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manufacturing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Média</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Not For Profit</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Recreation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Retail</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Shipping</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Technology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Telecommunications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Transportation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Utilities</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>LeadSource</picklist>
            <values>
                <fullName>Manual Input</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Webform</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Import</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Email</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Lead provider</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Social</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Contracting webform</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>External provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Adwords</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Referral Programm</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Incoming call</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>SEO</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>MQL %28Marketing Qualified Lead%29</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Trade Fairs</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Display</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Content marketing</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Phone</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Agency</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Chat</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Case</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CPL Provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public tender</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>E-mailing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Direct Mail</fullName>
                <default>false</default>
            </values>
			<values>
                <fullName>Fax Mailing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Direct order without contract</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Marketing campaigns %28precise%29</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>EFG</fullName>
                <default>false</default>
            </values> 
        </picklistValues>
        <picklistValues>
            <picklist>Rating</picklist>
            <values>
                <fullName>Cold</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Hot</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Warm</fullName>
                <default>false</default>
            </values>
        </picklistValues>
		<picklistValues>
            <picklist>Name</picklist>
            <values>
                <fullName>Mr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mrs%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ms%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mx.</fullName>
                <default>false</default>
            </values>
        </picklistValues>
    </recordTypes>
    <recordTypes>
        <fullName>ER_Merchant_Lead_RT</fullName>
        <active>true</active>
        <businessProcess>Merchant Lead Process</businessProcess>
        <label>Merchant Lead</label>
        <picklistValues>
            <picklist>ERCZ_Academic_Title__c</picklist>
            <values>
                <fullName>Bc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BcA%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CSc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DSc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DiS%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Dr.</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DrSc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ing%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ing%2E arch%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>JUDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MDDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MSDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MUDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MVDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MgA%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mgr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PaedDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ph%2ED%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PhDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PhMr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PharmDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RCDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RNDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RSDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RTDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Th%2ED%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ThDr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ThLic%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ThMgr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>as%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>doc%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>odb%2E as%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>p%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pan</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>paní</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pi</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>prof%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pí</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>pí%2E</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_BUPicklist__c</picklist>
            <values>
                <fullName>AE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>AR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>AT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BENELUX</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BG</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>BR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CEN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CZ</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ES</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FI</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>GR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>HU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>IT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>JP</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LB</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>LU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MA</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MD</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MX</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MY</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PC</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PT</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>RU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SG</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SK</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>TR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>TW</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>UK</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>US</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>UTA</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>VN</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_High_level_activity__c</picklist>
            <values>
                <fullName>Restaurant %26 Food</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Retail</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Industry__c</picklist>
            <values>
                <fullName>Agriculture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Apparel</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Association</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Automotive</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Banking</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Biotechnology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Call Center %26 Telecommunications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Care Services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chemicals</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chemicals %26 Plastic</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Committee of social affairs</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Communications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Communications agency</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Construction</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Consulting</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Council house</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Culture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Departmental council</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Education</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Electronics</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Energy</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Engineering</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Entertainment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Environmental</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Fabrics %26 Linen items</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Facilities</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Finance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Financial and Banking services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Food %26 Beverage</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Franchise</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Furniture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Gambling</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Gas Station</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Government</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Government VIP</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Healthcare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Hospitality</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Independent</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Insurance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Intercommunal cooperation services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lottery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Machinery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manufacturing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Marketing and advertising agencies</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Media</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mining</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ministry</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mixed-ownership company</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Non-profit organization</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Not For Profit</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Pharmaceuticals</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Private company</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public company</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public establishment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Publishing %26 Paper</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Real estate</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Recreation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Regional council</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Retail</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Security</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Services</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Shipping</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Social institution</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Sport</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Technology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Technology %26 Computer Science</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Telecommunications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Tourism</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Transportation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Transportation %26 Logistics</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Utilities</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Wholesale Trade</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Work Agency</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Works concil</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Language__c</picklist>
            <values>
                <fullName>CZ</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>DE</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>EN</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ES</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>FR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>HU</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>NL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>PL</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SK</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Lead_Channel__c</picklist>
            <values>
                <fullName>Auto Import</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Client</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Customer</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Internal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lead provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Merchant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Trade association</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>User</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Unmatched</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Lead_Type__c</picklist>
            <values>
                <fullName>Merchant</fullName>
                <default>true</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Lead_sub_source__c</picklist>
            <values>
                <fullName>Abandonned contracting</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Adwords%3A Google%2C Bing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Encore</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Internal Webform</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lead provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other adwords</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public Register - list of existing companies</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public Register - list of new created companies</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Legal_Status__c</picklist>
            <values>
                <fullName>Active</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Closed</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Product_Family_Interest__c</picklist>
            <values>
                <fullName>ERHU_Edenred_Culture_%26_Sport_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Gift_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Schooling_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Gift</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Incentive</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Spendeo</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Sport and Culture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_TR</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Corporate Payment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Employee_benefits_quality_of_life</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Fleet</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Incentives %26 rewards</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Mobility</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Public Social Programs</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Ticket_Restaurant_Food</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ER_Ticket_Restaurant_Meal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERPL_Ticket Holiday</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Rejected_Reason__c</picklist>
            <values>
                <fullName>Acceptor added%2Fupdated</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Already Merchant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Company does not exist %2F Out of business</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Duplicate Lead</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Inability to contact</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No Terminal</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No budget</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No interest</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Unreachable after call back actions</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Spam</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Role__c</picklist>
            <values>
                <fullName>Accountant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Administrative employee</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Administrator</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Assistant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CFO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>HR Director</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Independant administrator</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Management assistant</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Owner</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Purchasing Manager</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Treasurer</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Solution__c</picklist>
            <values>
                <fullName>ERHU_Edenred_Culture_%26_Sport_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Gift_Card</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>ERHU_Edenred_Schooling_Card</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>ER_Terminal__c</picklist>
            <values>
                <fullName>Don%27t know</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>No</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Yes</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Industry</picklist>
            <values>
                <fullName>Agriculture</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Apparel</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Banking</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Biotechnology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chemicals</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Communications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Construction</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Consulting</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Education</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Electronics</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Energy</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Engineering</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Entertainment</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Environmental</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Finance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Food %26 Beverage</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Gas Station</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Government</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Healthcare</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Hospitality</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Insurance</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Machinery</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manufacturing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Not For Profit</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Recreation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Retail</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Shipping</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Technology</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Telecommunications</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Transportation</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Utilities</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>LeadSource</picklist>
            <values>
                <fullName>Agency</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Aggregator</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Case</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Chat</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Contracting webform</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Email</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Import</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Lead provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Manual Input</fullName>
                <default>true</default>
            </values>
            <values>
                <fullName>Marketplace</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Other</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>POS providers</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Phone</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Social</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Unmatched MCC</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Webform</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>CPL Provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>External provider</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Adwords</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Referral Programm</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Public tender</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>E-mailing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Direct Mail</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Fax Mailing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Incoming call</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>SEO</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>MQL (Marketing Qualified Lead)</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Trade Fairs</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Display</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Direct order without contract</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Marketing campaigns (precise)</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Content marketing</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Auto Enrollment</fullName>
                <default>false</default>
            </values>
        </picklistValues>
        <picklistValues>
            <picklist>Rating</picklist>
            <values>
                <fullName>Cold</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Hot</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Warm</fullName>
                <default>false</default>
            </values>
        </picklistValues>
		<picklistValues>
            <picklist>Name</picklist>
            <values>
                <fullName>Mr%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mrs%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Ms%2E</fullName>
                <default>false</default>
            </values>
            <values>
                <fullName>Mx.</fullName>
                <default>false</default>
            </values>
        </picklistValues>
    </recordTypes>
    <searchLayouts>
        <customTabListAdditionalFields>FULL_NAME</customTabListAdditionalFields>
        <customTabListAdditionalFields>LEAD.COMPANY</customTabListAdditionalFields>
        <customTabListAdditionalFields>LEAD.PHONE</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>FULL_NAME</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LEAD.COMPANY</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>FULL_NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LEAD.COMPANY</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LEAD.PHONE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LEAD.MOBILE_PHONE</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>FULL_NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.COMPANY</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.RECORDTYPE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ERBE_Initial_Product_Of_Interest__c</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.LEAD_SOURCE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.PHONE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.MOBILE_PHONE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.EMAIL</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LEAD.STATUS</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.LAST_NAME</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
    <validationRules>
        <fullName>VRCZ01_CRN_FORMAT_VALIDATION</fullName>
        <active>true</active>
        <description>If BU = CZ

Country code = CZ the CRN , if not empty, must be with 8 characters long with only numbers</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; ISPICKVAL(ER_BUPicklist__c, &apos;CZ&apos;)
&amp;&amp; NOT(ISBLANK(ER_Registration_Number__c))
&amp;&amp; NOT(REGEX(ER_Registration_Number__c, &quot;^[0-9]{8}$&quot;))</errorConditionFormula>
        <errorMessage>VRCZ01 - CRN must be 8 characters long with only numbers</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRFI01_CRN_Format</fullName>
        <active>true</active>
        <description
    >This validation rule ensure that the format of the CRNis correctly captured by User, and follow the finnish format. This rule can be bypassed by a User by checking the field &apos;BypassVR&apos; on its account.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c) &amp;&amp;
    ISPICKVAL( ER_BUPicklist__c, &apos;FI&apos;) &amp;&amp;
    NOT(REGEX( ER_Registration_Number__c , &quot;[0-9]{7}-[0-9]{1}&quot;))</errorConditionFormula>
        <errorDisplayField>ER_Registration_Number__c</errorDisplayField>
        <errorMessage
    >The Company Registration Number must be 9 alphanumeric characters long with the following format: 0000000-0 (7 digits then &quot;-&quot; then 1 digit)</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRFR01_Valid_CompanyRegistrationNumber</fullName>
        <active>true</active>
        <description
    >Validation rule created for respecting the required format for the field ER_Registration_Number__c.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; ER_SmartER_Context__c= True
&amp;&amp; ISPICKVAL(  ER_Country_Code__c , &apos;FR&apos;)
&amp;&amp; NOT(ISBLANK(ER_Registration_Number__c))
&amp;&amp; NOT(REGEX( ER_Registration_Number__c, &quot;[0-9]{14}&quot;))</errorConditionFormula>
        <errorDisplayField>ER_Registration_Number__c</errorDisplayField>
        <errorMessage>The Registration Number field must be 14 digits long without space.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRFR02_Valid_VATNumber</fullName>
        <active>true</active>
        <description
    >Validation rule created for respecting the required format for the field ER_VAT_Number__c.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
    &amp;&amp; ER_SmartER_Context__c= True 
    &amp;&amp; NOT(ISBLANK(ER_VAT_Number__c)) 
    &amp;&amp; ISPICKVAL(  ER_Country_Code__c , &apos;FR&apos;) 
    &amp;&amp; NOT(
    REGEX( ER_VAT_Number__c, &quot;[0-9]{11}&quot;) ||
    REGEX( ER_VAT_Number__c, &quot;[^IO]{1}[0-9]{10}&quot;) ||
    REGEX( ER_VAT_Number__c, &quot;[^IO]{2}[0-9]{9}&quot;) ||
    REGEX( ER_VAT_Number__c, &quot;FR[0-9A-Z]{2}[0-9]{9}&quot;) ||
    REGEX( ER_VAT_Number__c, &quot;[0-9]{1}[^IO]{1}[0-9]{9}&quot;)
    )</errorConditionFormula>
        <errorDisplayField>ER_VAT_Number__c</errorDisplayField>
        <errorMessage>The VAT field must have the following structure “FR + 2 check digits + 9 digits”</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER001_Lead_Conversion_Mandatory</fullName>
        <active>true</active>
        <description>The fields Company, Email and LastName must be filled in order to convert the Lead</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
 IsConverted = true,
OR(
ISBLANK(Company),
ISBLANK(LastName),
ISBLANK(FirstName)))</errorConditionFormula>
        <errorMessage
    >The fields Company, LastName and FirstName must be filled in order to convert the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER02_Lead_Solution_Obligatory</fullName>
        <active>false</active>
        <description
    >Solution field is obligatory for the lead coming from Merchant or Unmatched Transactions</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
RecordType.DeveloperName = &quot;ER_Merchant_Lead_RT&quot;,
OR(
ISPICKVAL(ER_Lead_Channel__c ,&quot;User&quot;),
ISPICKVAL(ER_Lead_Channel__c ,&quot;Customer&quot;)), 
(ISBLANK(TEXT( ER_Solution__c ))
))</errorConditionFormula>
        <errorDisplayField>ER_Solution__c</errorDisplayField>
        <errorMessage>The field Solution must be filled in order to register the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER03_Lead_StoreName_Obligatory</fullName>
        <active>true</active>
        <description
    >Information about Store Name is obligatory for the Led Channel = Unmatched, User, Client</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
            &amp;&amp; RecordType.DeveloperName = &quot;ER_Merchant_Lead_RT&quot;
            &amp;&amp; ISBLANK (ER_Store_Name__c)
            &amp;&amp; NOT(ISPICKVAL(ER_Lead_Channel__c, &quot;Merchant&quot;))
            &amp;&amp; TEXT(Status) = $Label.LABS_SF_Lead_Status_Qualification</errorConditionFormula>
        <errorDisplayField>ER_Store_Name__c</errorDisplayField>
        <errorMessage>VRER03 - The field Store Name must be filled in order to register the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER04_Lead_Address_Obligatory</fullName>
        <active>true</active>
        <description>Address information is obligatory for the User and Customer lead channel</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
            &amp;&amp; RecordType.DeveloperName = &quot;ER_Merchant_Lead_RT&quot;
            &amp;&amp; TEXT(Status) = $Label.LABS_SF_Lead_Status_Qualification
            &amp;&amp; ISBLANK (Street)
            &amp;&amp; (ISPICKVAL(ER_Lead_Channel__c ,&quot;User&quot;) || ISPICKVAL(ER_Lead_Channel__c ,&quot;Customer&quot;))</errorConditionFormula>
        <errorDisplayField>Street</errorDisplayField>
        <errorMessage>The address information must be filled in order to register the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER05_Lead_Registration_Mandatory</fullName>
        <active>true</active>
        <description>On the lead creation Salutation and First Name are obligatory</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
            &amp;&amp; NOT(ISPICKVAL(Status,&quot;New&quot;))
            &amp;&amp; NOT(ISPICKVAL(Status,&quot;Rejected&quot;))
            &amp;&amp; ER_SmartER_Context__c = FALSE
            &amp;&amp; (ISBLANK(TEXT(Salutation)) || ISBLANK(FirstName))</errorConditionFormula>
        <errorMessage>The fields Salutation and First Name must be filled in order to save the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER06_Lead_VATOrRegistrationId_Convert</fullName>
        <active>true</active>
        <description>VAT Number or Registration ID or Other Fiscal ID is Mandatory for Lead Conversion</description>
        <errorConditionFormula>AND(
        NOT($User.ER_BypassVR__c),  
        ER_Business_Unit__c != &#39;BE&#39;,
        AND( 
        IsConverted =true,
        ISBLANK(ER_VAT_Number__c), 
        ISBLANK( ER_Registration_Number__c),
        ISBLANK( ER_Other_Fiscal_ID__c)
        )
        )</errorConditionFormula>
        <errorMessage
    >Filling VAT Number or Company Registration Number or Other Fiscal ID is mandatory for conversion</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER07_Lead_Rejected_Reason</fullName>
        <active>true</active>
        <description>User should precised lead rejected reason on case of lead status = rejected</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
ISPICKVAL(Status,&quot;Rejected&quot;),
ISBLANK(TEXT(ER_Rejected_Reason__c)))</errorConditionFormula>
        <errorDisplayField>ER_Rejected_Reason__c</errorDisplayField>
        <errorMessage>The Rejected Reason must be filled in order to save the record</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER08_Lead_MID_Mandatory</fullName>
        <active>true</active>
        <description>When Lead Channel is equal to &quot;Unmatched&quot; --&gt; The MID is mandatory</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c), 
RecordType.DeveloperName = &quot;ER_Merchant_Lead_RT&quot;, 
ISBLANK ( ER_MID__c ),
ISPICKVAL( ER_Lead_Channel__c ,&quot;Unmatched&quot;))</errorConditionFormula>
        <errorMessage>The field MID must be filled in order to register the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER09_Lead_NbOfOutlets_Mandatory</fullName>
        <active>true</active>
        <description>When the Lead channel is &quot;Merchant&quot; --&gt; Number of outlets is mandatory</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
            &amp;&amp; RecordType.DeveloperName = &quot;ER_Merchant_Lead_RT&quot;
            &amp;&amp; TEXT(Status) = $Label.LABS_SF_Lead_Status_Qualification
            &amp;&amp; ISBLANK (ER_Number_of_outlets__c)
            &amp;&amp; ISPICKVAL(ER_Lead_Channel__c, &quot;Merchant&quot;)</errorConditionFormula>
        <errorMessage>VRER09 - The field Number Of Outlets must be filled in order to register the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER10_Email_OR_PhoneNumber_required</fullName>
        <active>true</active>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
ISPICKVAL(Status,&quot;Qualified&quot;),
AND(
Email == null,
AND (Phone == null, MobilePhone == null)
))</errorConditionFormula>
        <errorMessage>E-mail or Phone/Mobile phone number must be filled in order to convert the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER12_Check_Website_Format</fullName>
        <active>true</active>
        <description>This VR checks the website url format</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; NOT(ISBLANK(Website))
&amp;&amp; NOT(REGEX(Website,&apos;^((https|http|ftp)://)?([A-Za-z0-9]\\.|[A-Za-z0-9][A-Za-z0-9-]{0,61}[A-Za-z0-9]\\.){1,3}[A-Za-z]{2,6}(/|)&apos;))</errorConditionFormula>
        <errorMessage>VRER12 - Invalid website url format.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER13_Smarter_Country_Code_Mandatory</fullName>
        <active>true</active>
        <description>The Country code is mandatory for Smarter Leads</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; ER_SmartER_Context__c
&amp;&amp; ISPICKVAL(ER_Country_Code__c, &apos;&apos;)</errorConditionFormula>
        <errorMessage>VRER13 - Country Code cannot be empty</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRER14_Smarter_Data_Mandatory_For_BA</fullName>
        <active>true</active>
        <description>This VR to track the mandatory data required for the Billing Account creation</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; ER_SmartER_Context__c
&amp;&amp; IsConverted
&amp;&amp; (ISBLANK(Email) 
			|| ISBLANK(Street) 
				|| ISBLANK(PostalCode) 
				|| ISBLANK(City) 
				|| ISBLANK(Country) 
				|| ISPICKVAL(ER_Country_Code__c, &apos;&apos;))</errorConditionFormula>
        <errorMessage>VRER14 - The following fields are required for the Billing Account creation:
- Email
- Address</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRERPL001_Lead_Conversion_Mandatory</fullName>
        <active>false</active>
        <description>The fields Company, Email and LastName must be filled in order to convert the Lead</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
 IsConverted = true,
 RecordType.DeveloperName = &apos;ER_Client_Lead_RT&apos;,
 ISPICKVAL( ER_BUPicklist__c ,&quot;PL&quot;),
OR(
ISBLANK( ER_VAT_Number__c ))
)</errorConditionFormula>
        <errorMessage>The field VAT Number must be filled in order to convert the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRGR01_CRN_VAT_Format</fullName>
        <active>true</active>
        <description>CRN and VAT Number must be 9 characters long with ONLY Numbers.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c) &amp;&amp;
            ISPICKVAL(ER_BUPicklist__c, &quot;GR&quot;) &amp;&amp;
            ISPICKVAL(ER_Country_Code__c, &quot;GR&quot;) &amp;&amp;
            (NOT(ISBLANK(ER_Registration_Number__c)) &amp;&amp;
            ((NOT(REGEX(ER_Registration_Number__c, &quot;^[0-9]*$&quot;)) ||
            LEN(ER_Registration_Number__c) &lt;&gt; 9)
            ) ||
            (NOT(ISBLANK(ER_VAT_Number__c)) &amp;&amp;
            (NOT(REGEX(ER_VAT_Number__c, &quot;^[0-9]*$&quot;)) ||
            LEN(ER_VAT_Number__c) &lt;&gt; 9)
            ))</errorConditionFormula>
        <errorMessage>VRGR01 - CRN and VAT Number must be 9 characters long with only numbers</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRHU01_Lead_PhoneNumber_HungarianFormat</fullName>
        <active>true</active>
        <description
    >This validation rule will control the phone number format to ensure it fit Hungarian format, as following : 
+36-1-111-1111 (capital city) 
+36-11-111-111 (countryside)</description>
        <errorConditionFormula>AND( 
NOT($User.ER_BypassVR__c),
ISPICKVAL(ER_BUPicklist__c, &apos;HU&apos;), 
NOT( 
OR( 
LEN(Phone) = 0, 
REGEX(Phone, &quot;[+36]{3}-[0-9]{1}-[0-9]{3}-[0-9]{4}&quot;), 
REGEX(Phone, &quot;[+36]{3}-[0-9]{2}-[0-9]{3}-[0-9]{3}&quot;) 
)))</errorConditionFormula>
        <errorDisplayField>Phone</errorDisplayField>
        <errorMessage>The format of the Phone Number must be equal to Hungarian format, as following : 
+36-1-111-1111 (capital city) 
+36-11-111-111 (countryside)</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRHU02_Lead_MobilePhone_HungarianFormat</fullName>
        <active>true</active>
        <description
    >This validation rule will control the mobile phone format to ensure it fit Hungarian format, as following : 
+36-11-111-1111 (mobile phones)</description>
        <errorConditionFormula>AND( 
NOT($User.ER_BypassVR__c),
ER_Business_Unit__c = &apos;HU&apos;,  
NOT( 
OR( 
LEN(MobilePhone) = 0, 
REGEX(MobilePhone, &quot;[+36]{3}-[0-9]{2}-[0-9]{3}-[0-9]{4}&quot;) 
)))</errorConditionFormula>
        <errorDisplayField>MobilePhone</errorDisplayField>
        <errorMessage>The format of the Mobile Phone must be equal to Hungarian format, as following : 
+36-11-111-1111 (mobile phones)</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRHU03_Lead_VATNumber_HungarianFormat</fullName>
        <active>true</active>
        <description
    >This validation rule ensure that the format of the VAT Number is correctly captured by User, and follow the hungarian format. This rule can be bypassed by a User by checking the field &apos;BypassVR&apos; on its account.</description>
        <errorConditionFormula>AND( 
NOT($User.ER_BypassVR__c),
ER_Business_Unit__c = &apos;HU&apos;,
ISPICKVAL(Status,&quot;Qualified&quot;), 
NOT( 
OR( 
ISBLANK(ER_VAT_Number__c), 
REGEX(ER_VAT_Number__c , &quot;[0-9]{8}-[0-9]{1}-[0-9]{2}&quot;) 
)))</errorConditionFormula>
        <errorDisplayField>ER_VAT_Number__c</errorDisplayField>
        <errorMessage>The format of the VAT Number must match Hungarian format : 11 digits xxxxxxxx-x-xx</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE09_Valid_Mobile</fullName>
        <active>true</active>
        <description>This rule makes sure the Mobile Number field follows a prescribed format.</description>
        <errorConditionFormula>AND( 
    NOT($User.ER_BypassVR__c),
    ISPICKVAL(ER_Country_Code__c, &apos;BE&apos;),
    NOT(REGEX( MobilePhone ,&quot;(^$)|^[+]?([0-9]*[/\\.\\s\\-\\(\\)]|[0-9]+){3,24}$&quot;)), 
    NOT( $Profile.Name = &quot;Integration Profile&quot;), 
    NOT(ISBLANK(MobilePhone)) 
    )</errorConditionFormula>
        <errorMessage>Please fill in a valid Mobile Phone number.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE10_Valid_Email</fullName>
        <active>true</active>
        <description>valid email</description>
        <errorConditionFormula>AND(
    NOT($User.ER_BypassVR__c),
    ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;), 
    NOT(REGEX(  Email ,&quot;(^$)|^([a-zA-Z0-9#_\\-\\.\\+\\&apos;]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,63}|[0-9]{1,3}|[a-zA-Z]{2,63}[0-9])(\\]?)$&quot;)), 
    NOT( $Profile.Name = &quot;Integration Profile&quot;), 
    NOT(ISBLANK( Email )))</errorConditionFormula>
        <errorMessage>Please fill in a valid Email address.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE11_Valid_Phone</fullName>
        <active>true</active>
        <description>This rule makes sure the Phone Number field follows a prescribed format.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; $Setup.ER_Validation_Rules_Settings__c.VRBE11_Valid_Phone__c = false
&amp;&amp; ISPICKVAL(ER_Country_Code__c, &quot;BE&quot;)
&amp;&amp; NOT(ISBLANK(Phone))
&amp;&amp; NOT(REGEX( Phone ,&quot;(^$)|^[+]?([0-9]*[/\\.\\s\\-\\(\\)]|[0-9]+){3,24}$&quot;))</errorConditionFormula>
        <errorMessage>Please fill in a valid phone number.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE12_Mobile_or_phone_required</fullName>
        <active>false</active>
        <description>Validation rule that enforces the user to fill in the phone nr of the mobile nr.</description>
        <errorConditionFormula>AND(
    NOT($User.ER_BypassVR__c),
    ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;),
    NOT(ISBLANK( FirstName )), 
    NOT(ISBLANK( LastName )), 
    ISBLANK(MobilePhone ), 
    ISBLANK(Phone ))</errorConditionFormula>
        <errorMessage>Phone nr of Mobile nr needs to be filled in.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE13_Valid_VATnr</fullName>
        <active>true</active>
        <description>Enter the VAT number in the correct format.
            Ex. LU24665334</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
            &amp;&amp; $Setup.ER_Validation_Rules_Settings__c.VRBE03_Valid_VATnr__c = false
            &amp;&amp; ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;)
            &amp;&amp; NOT(ISBLANK(ER_VAT_Number__c))
            &amp;&amp; ( (IF( LEFT(ER_VAT_Number__c, 2) = &apos;BE&apos;, NOT(REGEX(ER_VAT_Number__c, &quot;BE[0-1]{1}[0-9]{9}&quot;)), FALSE))
            || (IF(LEFT(ER_VAT_Number__c, 2) = &apos;LU&apos;,
            NOT(REGEX(ER_VAT_Number__c, &quot;LU[0-9]{8}&quot;)) || MOD(VALUE(LEFT(RIGHT(ER_VAT_Number__c, 8), 6)), 89) != VALUE(RIGHT(ER_VAT_Number__c ,2)), FALSE))
            )</errorConditionFormula>
        <errorMessage>VRBE13 - Please enter the VAT number in the correct format.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE14_VAT_Not_Blank</fullName>
        <active>true</active>
        <description>If Subject to VAT, VAT must be filled.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; IsConverted = true
&amp;&amp; $Setup.ER_Validation_Rules_Settings__c.VRBE14_VAT_Not_Blank__c = False
&amp;&amp; ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;)
&amp;&amp; ISBLANK(ER_VAT_Number__c)
&amp;&amp; NOT(ER_Not_Subject_to_VAT__c)</errorConditionFormula>
        <errorMessage>VRBE14 - The VAT Number must be filled if the Company is subject to VAT.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE15_Nr_of_beneficiaries_vs_Nr_of_empl</fullName>
        <active>true</active>
        <description>The number of beneficiaries cannot exceed the number of employees</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; (ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;))
&amp;&amp; ER_Number_Of_Beneficiaries__c  &gt;  NumberOfEmployees</errorConditionFormula>
        <errorMessage>The number of beneficiaries cannot exceed the number of employees.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE16_Conversion_Mandatory</fullName>
        <active>true</active>
        <description
    >The fields FirstName, Email, Legal Country Code should be filled in order to convert the Lead</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
            &amp;&amp; ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;)
            &amp;&amp; IsConverted
            &amp;&amp; (ISBLANK(Email) 
            || ISBLANK(FirstName) 
            || AND( ISPICKVAL(ER_Legal_Country_Code__c , &apos;&apos;), NOT(ISBLANK (ER_Legal_Street__c)),NOT(ISBLANK ( ER_Legal_City__c )),NOT(ISBLANK ( ER_Legal_Zip_Code__c  ))))</errorConditionFormula>
        <errorMessage
    >The fields FirstName, Email, Legal Country Code should be filled in order to convert the Lead</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRBE17_Conversion_Mandatory2</fullName>
        <active>true</active>
        <description>The country code is mandatory if the store name and address is filled in</description>
        <errorConditionFormula>AND(NOT($User.ER_BypassVR__c),
            ISPICKVAL(ER_BUPicklist__c, &apos;BENELUX&apos;),
            IsConverted,
            OR(AND(ISPICKVAL(ER_Country_Code__c,&apos;&apos;),(NOT(ISBLANK( Address))))),(NOT(ISBLANK( ER_Store_Name__c )))
            )</errorConditionFormula>
        <errorMessage>Please fill in a country code for the store</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRRO02_Mobile_Romanian_Format</fullName>
        <active>true</active>
        <description>This validation rule will control the Mobile number format to ensure it fits a maximum of 10 numeric characters.</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
NOT(ISBLANK(MobilePhone)),
ISPICKVAL(ER_BUPicklist__c,&apos;RO&apos;),
NOT(REGEX(MobilePhone, &quot;^(0)[1-9]{1}([0-9]{8,9})$&quot;))
)</errorConditionFormula>
        <errorDisplayField>MobilePhone</errorDisplayField>
        <errorMessage>Mobile phone number should contain 10 numeric characters (e.g. 0711123123)</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRRO03_Phone_Romanian_Format</fullName>
        <active>true</active>
        <description>This validation rule will control the phone number format to ensure it fits no less than 10 and no more than 13 characters.</description>
        <errorConditionFormula>AND(
NOT($User.ER_BypassVR__c),
NOT(ISBLANK(Phone)),
ISPICKVAL(ER_BUPicklist__c,&apos;RO&apos;),
NOT(OR(
REGEX(Phone, &quot;(\\d){10}&quot;),
REGEX(Phone, &quot;(\\d){13}&quot;)
)
)
)</errorConditionFormula>
        <errorDisplayField>Phone</errorDisplayField>
        <errorMessage>Phone should contain a minimum number of 10 and a maximum number of 13 numeric characters in the form (e.g. 0231123123 or 0040123123123)</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRRO04_VAT_Romanian_Mandatory</fullName>
        <active>true</active>
        <errorConditionFormula>NOT($User.ER_BypassVR__c) &amp;&amp;
ISPICKVAL(ER_BUPicklist__c,&apos;RO&apos;) &amp;&amp;
NOT( ISPICKVAL(Status, &apos;Rejected&apos;) ) &amp;&amp;
NOT(ER_Not_Subject_to_VAT__c) &amp;&amp; 
ISBLANK(ER_VAT_Number__c)</errorConditionFormula>
        <errorDisplayField>ER_VAT_Number__c</errorDisplayField>
        <errorMessage>Please enter a VAT Number.</errorMessage>
    </validationRules>
    <validationRules>
        <fullName>VRRO05_Lead_BlockHBDuplicate_Conversion</fullName>
        <active>true</active>
        <description>Block conversion for Hubspot leads that are potential duplicates.</description>
        <errorConditionFormula>NOT($User.ER_BypassVR__c)
&amp;&amp; ISPICKVAL(ER_BUPicklist__c, &apos;RO&apos;)
&amp;&amp; IsConverted
&amp;&amp; ISPICKVAL(ER_Sync_to_HubSpot__c, &apos;No&apos;)
&amp;&amp; NOT(ISBLANK(ERRO_Hubspot_Source__c))</errorConditionFormula>
        <errorMessage>This Lead is a potential duplicate in Hubspot.Please check in both Salesforce and Hubspot.</errorMessage>
    </validationRules>
    <webLinks>
        <fullName>GoogleMaps</fullName>
        <availability>online</availability>
        <displayType>link</displayType>
        <encodingKey>UTF-8</encodingKey>
        <hasMenubar>false</hasMenubar>
        <hasScrollbars>true</hasScrollbars>
        <hasToolbar>false</hasToolbar>
        <height>600</height>
        <isResizable>true</isResizable>
        <linkType>url</linkType>
        <masterLabel>Google Maps</masterLabel>
        <openType>newWindow</openType>
        <position>none</position>
        <protected>false</protected>
        <showsLocation>false</showsLocation>
        <showsStatus>false</showsStatus>
        <url>http://maps.google.com/maps?f=q&amp;hl=en&amp;q={!Lead_Street}+{!Lead_City}+{!Lead_State}&amp;om=1</url>
    </webLinks>
    <webLinks>
        <fullName>GoogleNews</fullName>
        <availability>online</availability>
        <displayType>link</displayType>
        <encodingKey>UTF-8</encodingKey>
        <hasMenubar>false</hasMenubar>
        <hasScrollbars>true</hasScrollbars>
        <hasToolbar>false</hasToolbar>
        <height>600</height>
        <isResizable>true</isResizable>
        <linkType>url</linkType>
        <masterLabel>Google News</masterLabel>
        <openType>newWindow</openType>
        <position>none</position>
        <protected>false</protected>
        <showsLocation>false</showsLocation>
        <showsStatus>false</showsStatus>
        <url>http://www.google.com/news?&amp;q={!Lead_Company}&amp;btnG=Search+News</url>
    </webLinks>
    <webLinks>
        <fullName>GoogleSearch</fullName>
        <availability>online</availability>
        <displayType>link</displayType>
        <encodingKey>UTF-8</encodingKey>
        <hasMenubar>false</hasMenubar>
        <hasScrollbars>true</hasScrollbars>
        <hasToolbar>false</hasToolbar>
        <height>600</height>
        <isResizable>true</isResizable>
        <linkType>url</linkType>
        <masterLabel>Google Search</masterLabel>
        <openType>newWindow</openType>
        <position>none</position>
        <protected>false</protected>
        <showsLocation>false</showsLocation>
        <showsStatus>false</showsStatus>
        <url>http://www.google.com/search?q={!Lead_Company}</url>
    </webLinks>
</CustomObject>