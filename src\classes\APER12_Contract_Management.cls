/*
----------------------------------------------------------------------
-- - Name          : APER12_Contract_Management
-- - Author        : OLA
-- - Description   : Contract Management, This class is used to group all the functionnality linked to the object Contract
--
-- Date         Name                Version     Remarks
-- -----------  -----------         --------    ---------------------------------------
--  Jan-2019       OLA                 1.0         Initial version.
--  Jan-2020       OLA                 2.0         Add Offline Mode.
--  FEB-2021       VJOY                3.0         Add mapping for range fields(min, max, line type from QLI).
--  FEB-2021       KHAD                4.0         Solution at all level
--  APR-2021       KHAD                5.0         Contract Naming convention
--  SEP-2021       AAM                 6.0         Set ownerId from Quote in createContractFromQuote method (PBI#12394)
--  NOV-2023       VJOY                7.0         Set Address Field for RO from Related Account
--  NOV-2023       VJOY                8.0         Calculate End date based on duration for RO
---------------------------------------------------------------------------------------
*/
public class APER12_Contract_Management {
  public static List<Contract> createContractFromQuote(Map<id, Quote> acceptedQuoteMap) {
    List<Contract> contractToInsert = new List<Contract>();
    Contract newContract;
    Savepoint sp = Database.setSavepoint();
    Map<id, String> oppLeadSources = new Map<id, String>();

    try {
      for (Quote quote : [
        SELECT
          Id,
          OpportunityId,
          Opportunity.ER_Contract_Expected_Start_Date__c,
          Opportunity.LeadSource,
          AccountId,
          Pricebook2Id,
          CurrencyIsoCode,
          RecordType.DeveloperName,
          ER_Credit_Limit__c,
          ER_Indexation_Index__c,
          ER_InvoicingFrequency__c,
          ER_PaymentMethod__c,
          ER_PaymentTerms__c,
          Opportunity.ER_Solutions__c,
          ER_ReimbursementTerm__c,
          SK_Credit_Note_Frequency__c,
          SK_Payment_Terms_Voucher__c,
          ER_Contract_Duration__c,
          ER_SEPA_Bank_Account__c,
          ER_Indexation__c,
          ER_Original_Agreement__c,
          ER_Quote_Type__c,
          OwnerId,
          Opportunity.ER_BUPicklist__c,
          ERBE_Reimbursement_Statement_Frequency2__c,
          ERBE_Reimbursement_Statement_Support2__c,
          ERBE_Weekly_Frequence__c,
          ER_Invoicing_payment_delay__c,
          ER_Invoicing_day_of_week__c
        FROM Quote
        WHERE Id IN :acceptedQuoteMap.keySet()
      ]) {
        System.debug('APER12_Contract_Management : createContractFromQuote : Account : ' + quote.AccountId);
        oppLeadSources.put(quote.opportunityId, quote.Opportunity.LeadSource);

        newContract = new Contract();
        if (quote.RecordType.DeveloperName == 'ER_Client_Quote') {
          newContract.RecordTypeId = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName()
            .get('ER_Client_Contract_RT')
            .getRecordTypeId();
        } else if (quote.RecordType.DeveloperName == 'ER_Merchant_Quote') {
          newContract.RecordTypeId = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName()
            .get('ER_Merchant_Contract_RT')
            .getRecordTypeId();
        }
        newContract.ER_QuoteId__c = quote.Id;
        newContract.ER_OpportunityId__c = quote.OpportunityId;
        newContract.AccountId = quote.AccountId;
        newContract.Pricebook2Id = quote.Pricebook2Id;
        newContract.OwnerId = quote.OwnerId; // AAM-09.09.2021 : PBI#12394
        newContract.CurrencyIsoCode = quote.CurrencyIsoCode;
        newContract.ER_Credit_Limit2__c = quote.ER_Credit_Limit__c;
        newContract.ER_Indexation_Index2__c = quote.ER_Indexation_Index__c;
        newContract.ER_InvoicingFrequency2__c = quote.ER_InvoicingFrequency__c;
        newContract.ER_PaymentMethod2__c = quote.ER_PaymentMethod__c;
        newContract.ER_PaymentTerms2__c = quote.ER_PaymentTerms__c;
        newContract.ER_ReimbursementTerm2__c = quote.ER_ReimbursementTerm__c;
        newContract.SK_Credit_Note_Frequency2__c = quote.SK_Credit_Note_Frequency__c;
        newContract.SK_Payment_Terms_Voucher2__c = quote.SK_Payment_Terms_Voucher__c;
        newContract.ER_OppIndexation2__c = quote.ER_Indexation__c;
        newContract.ER_Contract_Type__c = quote.ER_Quote_Type__c;
        newContract.ER_Original_Agreement__c = quote.ER_Original_Agreement__c;
        newContract.StartDate = (quote.Opportunity.ER_Contract_Expected_Start_Date__c != null)
          ? quote.Opportunity.ER_Contract_Expected_Start_Date__c
          : Date.today();
        //newContract.StartDate = Date.today();
        newContract.ER_BUPicklist__c = quote.Opportunity.ER_BUPicklist__c; // AAM-21.01.2022 : PBI#14291
        newContract.ER_Contract_Duration__c = quote.ER_Contract_Duration__c;
        newContract.ER_Solutions__c = quote.Opportunity.ER_Solutions__c;
        newContract.ERBE_Reimbursement_Statement_Frequency__c = quote.ERBE_Reimbursement_Statement_Frequency2__c;
        newContract.ERBE_Reimbursement_statement_Support__c = quote.ERBE_Reimbursement_Statement_Support2__c;
        newContract.ER_SEPA_Bank_Account__c = quote.ER_SEPA_Bank_Account__c;
        newContract.ERBE_Invoicing_day_of_week__c = quote.ER_Invoicing_day_of_week__c;
        newContract.ERBE_Invoicing_Payment_Delay__c = quote.ER_Invoicing_payment_delay__c;
        newContract.ERBE_Weekly_Frequence__c = quote.ERBE_Weekly_Frequence__c;
        contractToInsert.add(newContract);
      }
      if (!contractToInsert.isEmpty()) {
        System.debug('contractToInsert : ' + contractToInsert);
        insert contractToInsert;

        for (Contract cntr : contractToInsert) {
          acceptedQuoteMap.get(cntr.ER_QuoteId__c).ContractId = cntr.Id;
          System.debug('acceptedQuoteMap : ' + acceptedQuoteMap);
        }
        createCLIAndUpdateOpportunity(acceptedQuoteMap);

        if (newContract.ER_Contract_Type__c == Label.LAB_SF_Contact_Operational) {
          APER20_Account_Management.manageAccountLineItemsForContract(newContract);
          APER30_FinancialCenter_Management.manageFCLIForFinancialCenter(newContract);
          if (
            newContract.RecordTypeId ==
            Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName()
              .get('ER_Merchant_Contract_RT')
              .getRecordTypeId()
          ) {
            APER14_Store_Management.manageSLIFromContractNew(newContract);
            APER23_Acceptor_Management.manageAcceptorLIFromContract(newContract);
          } else if (
            newContract.RecordTypeId ==
            Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName()
              .get('ER_Client_Contract_RT')
              .getRecordTypeId()
          ) {
            APER28_DeliverySite_Management.manageDSLIFromContract(newContract);
            APER29_DistributionPoint_Management.manageDPLIFromContract(newContract);
          }

          //Activate SLIs for Online Acquisition process
          if (
            oppLeadSources.get(newContract.ER_OpportunityId__c) == Constants.CONTRACTING_WEBFORM &&
            String.isNotBlank(newContract.ER_Solutions__c)
          )
            APER30_AutoEnrollment_Management.activateAutoenrollmentSLIs(
              newContract.AccountId,
              newContract.ER_Solutions__c
            );
        }
      }
    } catch (Exception e) {
      Database.rollback(sp);
    }
    return contractToInsert;
  }

  public static void createCLIAndUpdateOpportunity(Map<Id, Quote> acceptedQuoteMap) {
    Map<String, Date> mapProductEndDate = new Map<String, Date>();

    //Map<String, Date> endDateByproductCodeMap = new Map<String, Date>();

    ER_ContractLineItem__c cli;
    List<ER_ContractLineItem__c> cliToInsert = new List<ER_ContractLineItem__c>();
    List<Opportunity> opportunityToUpdate = new List<Opportunity>(); // update contract Id field
    Opportunity opportunityInst;

    for (Quote quoteInst : [
      SELECT
        Id,
        OpportunityId,
        AccountId,
        Pricebook2Id,
        ContractId,
        (
          SELECT
            Id,
            ListPrice,
            Discount,
            UnitPrice,
            ER_What__c,
            CurrencyIsoCode,
            PricebookEntryId,
            Product2Id,
            Product2.Name,
            Quantity,
            ER_Percentage__c,
            ER_NeedApproval__c,
            ER_Creation_date_in_OS__c,
            ER_Card_Operational_System_ID__c,
            ER_Minimum_Fee__c,
            ER_Line_Type__c,
            ER_Max__c,
            ER_Min__c,
            Quote.Opportunity.ER_Contract_Expected_Start_Date__c,
            ER_Offer_validity_end_date__c,
            Product2.RecordTypeId,
            ER_Duration__c,
            Product2.RecordType.Name,
            TECH_ContactStartDate__c,
            ER_Quantity_Unit_of_Measure__c,
            ER_Price_End_Date__c,
            Product2.ProductCode,
            ER_Price_Start_Date__c,
            Quote.OwnerId,
            Quote.Opportunity.ER_BUPicklist__c,
            ER_User_Discount__c,
            ER_Edenred_Margin_TUR__c,
            ER_Free_to_setup_the_margin__c,
            ER_MPV__c,
            ER_Cumulative_with_Promotions__c
          FROM QuoteLineItems
        )
      FROM Quote
      WHERE Id IN :acceptedQuoteMap.keySet()
    ]) {
      opportunityInst = new Opportunity(
        Id = quoteInst.OpportunityId,
        ContractId = acceptedQuoteMap.get(quoteInst.id).ContractId
      ); //(from trigger before update) ==> cannot get ContractId from Query Yet!

      opportunityToUpdate.add(opportunityInst);

      //Calculate Service Start Date
      for (QuoteLineItem qli : quoteInst.QuoteLineItems) {
        if (qli.ER_Line_Type__c == 'Discount') {
          if (qli.ER_Price_End_Date__c != null)
            mapProductEndDate.put(qli.Product2.ProductCode, qli.ER_Price_End_Date__c);
          else if (qli.ER_Duration__c != null) {
            Date contractStartDate = (qli.Quote.Opportunity.ER_Contract_Expected_Start_Date__c != null
              ? qli.Quote.Opportunity.ER_Contract_Expected_Start_Date__c
              : Date.today());
            mapProductEndDate.put(
              qli.Product2.ProductCode,
              contractStartDate?.addDays(Integer.valueOf(qli.ER_Duration__c))
            );
          }
        }
      }

      for (QuoteLineItem qli : quoteInst.QuoteLineItems) {
        cli = new ER_ContractLineItem__c();
        cli.Name = qli.Product2.Name;
        cli.ER_Contract__c = acceptedQuoteMap.get(quoteInst.id).ContractId; //(from trigger before update) ==> cannot get ContractId from Query Yet!
        cli.ER_Percentage__c = qli.ER_Percentage__c;
        cli.ER_Product2__c = qli.Product2Id;
        cli.ER_PricebookEntryId__c = qli.PricebookEntryId;
        cli.CurrencyIsoCode = qli.CurrencyIsoCode;
        cli.ER_UnitPrice__c = qli.UnitPrice;
        cli.ER_ListPrice__c = qli.ListPrice;
        cli.ER_Quantity__c = qli.Quantity;
        cli.ER_NeedApproval__c = qli.ER_NeedApproval__c;
        cli.ER_Minimum_Fee__c = qli.ER_Minimum_Fee__c;
        cli.ER_Card_Operational_System_ID__c = qli.ER_Card_Operational_System_ID__c;
        //cli.ER_Creation_date_in_OS__c = qli.ER_Creation_date_in_OS__c;
        cli.ER_Discount__c = qli.Discount;
        cli.ER_What__c = qli.ER_What__c;

        //FEB 2021 VJOY - Added mapping for range products
        cli.ER_Line_Type__c = qli.ER_Line_Type__c;
        cli.ER_Max__c = qli.ER_Max__c;
        cli.ER_Min__c = qli.ER_Min__c;

        /*PBI 7485: VJOY 2021*/
        cli.ER_Offer_validity_end_date__c = qli.ER_Offer_validity_end_date__c;
        cli.ER_Quantity_Unit_of_Measure__c = qli.ER_Quantity_Unit_of_Measure__c;
        Date contractStartDate = (qli.Quote.Opportunity.ER_Contract_Expected_Start_Date__c != null
          ? qli.Quote.Opportunity.ER_Contract_Expected_Start_Date__c
          : Date.today());

        if (cli.ER_Line_Type__c == 'Discount')
          cli.ER_Price_End_Date__c = mapProductEndDate.get(qli.Product2.ProductCode);
        else if (cli.ER_Line_Type__c == 'Service')
          cli.ER_Price_Start_Date__c = mapProductEndDate.get(qli.Product2.ProductCode)?.addDays(1);
        else
          cli.ER_Price_End_Date__c = null;

        cli.OwnerId = qli.Quote.OwnerId; // AAM 18.11.21: PBI#13620
        cli.ER_BUPicklist__c = qli.Quote.Opportunity.ER_BUPicklist__c; // AAM 18.11.21: PBI#14291

        /*PBI14186 VJOY JAN 2022*/
        cli.ER_User_Discount__c = qli.ER_User_Discount__c;
        cli.ER_Edenred_Margin_TUR__c = qli.ER_Edenred_Margin_TUR__c;
        cli.ER_Free_To_Set_Margin__c = qli.ER_Free_to_setup_the_margin__c;
        cli.ER_MPV__c = qli.ER_MPV__c;
        cli.Cumulative_with_Promotions__c = qli.ER_Cumulative_with_promotions__c;

        cliToInsert.add(cli);
      }
    }

    if (!cliToInsert.isEmpty()) {
      System.debug('cliToInsert : ' + cliToInsert);
      insert cliToInsert;
    }
    if (!opportunityToUpdate.isEmpty()) {
      System.debug('opportunityToUpdate : ' + opportunityToUpdate);
      APER04_Opportunity_Management.updateOpportunityContract(opportunityToUpdate);
    }
  }

  @Future
  public static void deleteContractFromQuote(Set<id> deniedQuoteContractids) {
    List<Contract> contractListToDelete = new List<Contract>();
    Contract contractToDelete = new Contract();

    for (Id contractId : deniedQuoteContractids) {
      contractToDelete = new Contract(Id = contractId);
      contractListToDelete.add(contractToDelete);
    }
    if (!contractListToDelete.isEmpty()) {
      System.debug('contractListToDelete : ' + contractListToDelete);
      delete contractListToDelete;
    }
  }

  @AuraEnabled
  public static List<Contract> getContract(String contractId) {
    return getContract(new List<Id>{ contractId });
  }

  /**
   * <AUTHOR> HEL
   * @description Get Contracts
   * @param contractIds
   * @return List
   */
  public static List<Contract> getContract(List<Id> contractIds) {
    return [
      SELECT
        Id,
        RecordTypeId,
        ER_Business_Unit__c,
        ER_InvoicingFrequency2__c,
        ER_ReimbursementTerm2__c,
        CustomerSigned.Email,
        CustomerSignedId,
        ERBE_Reimbursement_Statement_Frequency__c,
        Status,
        ER_Indexation__c,
        ER_OppIndexation2__c,
        ER_PaymentTerms2__c,
        ER_PaymentMethod2__c,
        Account.ER_VIP__c,
        Subscription__c,
        ER_ZuoraQuote__c,
        ERBE_Reimbursement_statement_Support__c,
        AccountId,
        Account.Name,
        StartDate,
        EndDate,
        ER_OpportunityId__c,
        ER_OpportunityId__r.ER_IsAmended__c,
        ER_OpportunityId__r.ER_AmendedFromContract__c,
        ER_OpportunityId__r.ER_AmendedFromOpportunity__r.ContractId,
        ContractNumber,
        ER_Creation_date_in_OS__c,
        CustomerSignedDate,
        ER_OpportunityId__r.ER_AmendedFromContract__r.StartDate,
        ER_Contract_Type__c,
        ER_Original_Agreement__c,
        ER_Original_Agreement__r.Status,
        ER_Original_Agreement__r.ER_Contract_Type__c,
        RecordType.DeveloperName,
        ER_OpportunityId__r.ER_SmartER_Context__c,
        ER_BUPicklist__c,
        (
          SELECT
            Id,
            ER_Contract__c,
            Name,
            ER_Solution__c,
            ER_ProductCode__c,
            ER_Creation_date_in_OS__c,
            ER_Product2__c,
            ER_Product2__r.Name
          FROM ER_ContractLineItems__r
        )
      FROM Contract
      WHERE Id IN :contractIds
    ];
  }

  public static Map<Id, ER_ContractLineItem__c> getContractLineItems(String contractId) {
    return new Map<Id, ER_ContractLineItem__c>(
      [
        SELECT
          id,
          Name,
          ER_Business_Unit__c,
          ER_Contract__r.ER_PaymentTerms2__c,
          ER_Contract__r.ER_PaymentMethod2__c,
          ER_Solution__c,
          ER_ProductCode__c,
          ER_Creation_date_in_OS__c,
          ER_Minimum_Fee__c,
          ER_Product2__c,
          ER_Product2__r.ER_Solution__c,
          ER_Contract__r.accountId,
          ER_Product2__r.ER_Do_Not_Synchronize__c
        FROM ER_ContractLineItem__c
        WHERE ER_Contract__c = :contractId
      ]
    );
  }

  public static Map<Id, ER_ContractLineItem__c> getAllActiveContractLineItems(Set<String> companyIds) {
    return new Map<Id, ER_ContractLineItem__c>(
      [
        SELECT
          id,
          Name,
          ER_Contract__r.ER_PaymentTerms2__c,
          ER_Contract__r.ER_PaymentMethod2__c,
          ER_Solution__c,
          ER_ProductCode__c,
          ER_Creation_date_in_OS__c,
          ER_Minimum_Fee__c,
          ER_Product2__c,
          ER_Product2__r.ER_Solution__c,
          ER_Contract__r.accountId
        FROM ER_ContractLineItem__c
        WHERE
          ER_Contract__r.status = :Label.LABS_SF_Contract_Status_Activated
          AND ER_Contract__r.AccountId IN :companyIds
      ]
    );
  }

  /*
      * LASFAR
      * 14/02/2019
      * contractAmendment : 2 cases:
                  1- Clone the opportunity linked to the current contract
                  2- Create new Opportunity from the current contract (contract came from data init)
      */
  @AuraEnabled
  public static Opportunity contractAmendment(String contractId, String opportunityId) {
    return APER04_Opportunity_Management.createOpportunityFromContract(contractId, opportunityId);
  }

  @AuraEnabled
  public static void activateContractController(Contract contract, String opportunityId, Boolean isOfflineBU) {
    if (contract != null) {
      List<Contract> contractToUpdate = new List<Contract>();

      contract.ER_Offline__c = isOfflineBU;
      if (contract.ER_Creation_date_in_OS__c == null)
        contract.ER_Creation_date_in_OS__c = Datetime.now();
      contract.Status = Label.LABS_SF_Contract_Status_Activated;
      contractToUpdate.add(contract);

      if (contract.ER_OpportunityId__c != null && contract.ER_OpportunityId__r.ER_IsAmended__c) {
        String contractId = String.isNotBlank(contract.ER_OpportunityId__r.ER_AmendedFromContract__c)
          ? contract.ER_OpportunityId__r.ER_AmendedFromContract__c
          : contract.ER_OpportunityId__r.ER_AmendedFromOpportunity__r.ContractId;
        Contract terminateContract = new Contract(Id = contractId);
        terminateContract.ER_Offline__c = isOfflineBU;
        terminateContract.ER_TerminationReason__c = Label.LAB_SF_Contract_Amendment;
        //Check if the contract is a zuora contract then assign the new contract start date - 1
        if (Schema.SObjectType.Contract.fields.ER_ZuoraQuote__c.isAccessible() && contract.ER_ZuoraQuote__c != null) {
          terminateContract.Status = Label.LABS_SF_Contract_Status_Pending_Termination;
          if (contract.StartDate == contract.ER_OpportunityId__r.ER_AmendedFromContract__r.StartDate)
            terminateContract.EndDate = contract.StartDate;
          else if (contract.StartDate > contract.ER_OpportunityId__r.ER_AmendedFromContract__r.StartDate)
            terminateContract.EndDate = contract.StartDate.addDays(-1);
        } else {
          terminateContract.Status = Label.LABS_SF_Contract_Status_Terminated;
          terminateContract.ER_Terminated_Date__c = Datetime.now();
          terminateContract.EndDate = Date.today();
        }
        contractToUpdate.add(terminateContract);
      }

      System.debug('## activateContractController update : ' + contractToUpdate);
      update contractToUpdate;

      //ALK - Manage Solution Line Items Asynchronously
      /*Distributed_Transaction_Event__e dte = new Distributed_Transaction_Event__e();
      dte.Transaction_Name__c = Constants.MANAGE_SOLUTION_LINE_ITEMS;
      dte.Transaction_Content__c = JSON.serializePretty(contract);
      EventBus.publish(dte);*/
    }
  }

  /**
   * <AUTHOR>
   * @date 02/01/2023
   * @description run solution line items management asynchronously
   * @param activatedContact
   */
  public static void manageSolutionLineItems(Contract activatedContact) {
    if (activatedContact != null && activatedContact.ER_Contract_Type__c == Label.LAB_SF_Contact_Operational) {

      //CloseWon the opportunity
      // OLA : Moved to Contract Trigger
      /*if (String.isNotBlank(activatedContact.ER_OpportunityId__c))
        setOpportunityToCloseWon(activatedContact.ER_OpportunityId__c);*/
      //OLA : Moved to Contract Trigger
      //APER20_Account_Management.manageStatusOnContractActivation(activatedContact.AccountId);
      APER20_Account_Management.manageAccountLineItemsForContract(activatedContact);

      if (!activatedContact.ER_OpportunityId__r.ER_SmartER_Context__c) {
        APER14_Store_Management.manageSLIFromContract(activatedContact.AccountId);
        APER30_FinancialCenter_Management.manageFCLIForFinancialCenter(activatedContact);
        if (
          activatedContact.RecordTypeId ==
          Utils.getRecordTypeIdByDeveloperName(Contract.SObjectType, Constants.CONTRACT_MERCHANT_RT)
        ) {
          APER14_Store_Management.manageSLIFromContractNew(activatedContact);
          APER23_Acceptor_Management.manageAcceptorLIFromContract(activatedContact);
        } else if (
          activatedContact.RecordTypeId ==
          Utils.getRecordTypeIdByDeveloperName(Contract.SObjectType, Constants.CONTRACT_CLIENT_RT)
        ) {
          APER28_DeliverySite_Management.manageDSLIFromContract(activatedContact);
          APER29_DistributionPoint_Management.manageDPLIFromContract(activatedContact);
        }
      }
    }
  }

  /**
   * <AUTHOR>
   * @date 17/01/2022
   * @param oppId
   */
  private static void setOpportunityToCloseWon(Id oppId) {
    GenericWithoutSharing.setOpportunityToCloseWon(oppId);
  }

  /*
   * LASFAR
   * 27/03/2019
   * terminateContract : For batch process, update status to Terminated when WS status is succeeded.
   * 02/01/2020 : Add Offline Mode.
   */
  public static void terminateContract(Contract contract, Boolean isOfflineBU) {
    contract.Status = Label.LABS_SF_Contract_Status_Terminated;
    contract.ER_Terminated_Date__c = Datetime.now();
    contract.ER_Offline__c = isOfflineBU;
    update contract;
    //Add a bypass for RO Client scope and not set Account status as Inactive if contracts linked to it are Terminated  
    if(APER10_User_Management.userBU != Constants.RO) {
      APER20_Account_Management.manageStatusOnContractTermination(contract.AccountId);      
    }     
    APER14_Store_Management.manageSLIFromContract(contract.AccountId);
  }

  /**
   * <AUTHOR>
   * @description Terminate Contracts
   * @param contracts
   */
  public static void terminatedContacts(List<Contract> contracts) {
    Set<Id> contactsIds = new Set<Id>();
    Map<Id, Contract> mapAmendedContracts = new Map<Id, Contract>();

    for (Contract c : contracts) {
      c.Status = Label.LABS_SF_Contract_Status_Terminated;
      c.ER_Terminated_Date__c = Datetime.now();
    }

    Database.SaveResult[] results = Database.update(contracts, false);

    for (Database.SaveResult sr : results) {
      if (sr.isSuccess())
        contactsIds.add(sr.getId());
    }

    // Deactivate accounts having no more active contracts
    if (!contactsIds.isEmpty())
      APER20_Account_Management.deactivateAccountsStatusForTerminatedContracts(contactsIds);
  }

  /*
   * LASFAR
   * 14/02/2019
   * deleteCLIFromContract : For each deleted contract delete linked contract line items (no Master detail RelationShip)
   */
  public static void deleteCLIFromContract(Map<id, Contract> contractMap) {
    List<ER_ContractLineItem__c> cliToDelete = new List<ER_ContractLineItem__c>();

    for (Contract contractInst : [
      SELECT Id, (SELECT id FROM ER_ContractLineItems__r)
      FROM Contract
      WHERE id = :contractMap.keyset()
    ]) {
      for (ER_ContractLineItem__c cliInst : contractInst.ER_ContractLineItems__r) {
        cliToDelete.add(cliInst);
      }
    }
    if (!cliToDelete.isEmpty()) {
      System.debug('#cliToDelete Size : ' + cliToDelete.size());
      delete cliToDelete;
    }
  }

  /*
   * KHAD
   * FEB 2021
   * Delete SLI Related to contract.
   */
  public static void deleteSLIFromContract(Map<id, Contract> contractMap) {
    List<ER_Solution_Line_Item__c> SliToDelete = new List<ER_Solution_Line_Item__c>();

    for (Contract contractInst : [
      SELECT Id, (SELECT id FROM Solution_Line_Items__r)
      FROM Contract
      WHERE id = :contractMap.keyset()
    ]) {
      for (ER_Solution_Line_Item__c cliInst : contractInst.Solution_Line_Items__r) {
        SliToDelete.add(cliInst);
      }
    }
    if (!SliToDelete.isEmpty()) {
      System.debug('#SliToDelete Size : ' + SliToDelete.size());
      delete SliToDelete;
    }
  }

  /**
   * <AUTHOR>
   * @date 28/10/2021
   * @description Check if contract product code overlap with another active contract when date is changed
   * @param newItems upatesContract
   * @param oldItems Contract
   * @return List of errors
   */
  public static void validateIfContractDateOverlap(Map<Id, Contract> newItems, Map<Id, Contract> oldItems) {
    
    if(APER10_User_Management.userBU != Constants.RO) {  

      List<Id> updatedActivationPeriod = new List<Id>();
      List<String> zuoraDeployedBU = Utils.splitValues(Utils.getGlobalSettingsValueFromKey('ZUORA_DEPLOYED_BUs'));
      List<String> bus = Utils.splitValues(Utils.getGlobalSettingsValueFromKey('Bypass_Active_Contract_Process'));
      List<Id> accountIds = new List<Id>();
      for (Contract myContract : newItems.values()) {
        if (
          !myContract.Status.equalsIgnoreCase('Draft') &&
          zuoraDeployedBU.contains(myContract.ER_Business_Unit__c) &&
          !bus.contains(myContract.ER_Business_Unit__c) &&
          !UtilsBypass.getCurrentUser().ER_BypassVR__c &&
          (myContract.StartDate != oldItems.get(myContract.Id).StartDate ||
          myContract.EndDate != oldItems.get(myContract.Id).EndDate)
        ) {
          updatedActivationPeriod.add(myContract.Id);
          accountIds.add(myContract.AccountId);
        }
      }

      if (!updatedActivationPeriod.isEmpty())
        retrieveExistingSolutionAndCompareDate(updatedActivationPeriod, accountIds, newItems);
    }
  }

  /**
   * <AUTHOR>
   * @date 14/09/2021
   * @description Check if the solution to be added exists already on an active contract having the same type
   * @param updatedActivationPeriod Contract where EndDate or StartDate has changed
   * @param accountIds Account for which the contracts has changed
   * @param newItems Contract object from trigger
   *
   */
  public static void retrieveExistingSolutionAndCompareDate(
    List<Id> updatedActivationPeriod,
    List<Id> accountIds,
    Map<Id, Contract> newItems
  ) {
    List<Contract> contractList = getContract(updatedActivationPeriod);
    Map<Id, Account> merchantList = getExistingSolutionByAccounts(accountIds);
    for (Contract con : contractList) {
      String scope = (con.RecordType.DeveloperName == Constants.CONTRACT_MERCHANT_RT
        ? Constants.MERCHANT
        : Constants.CLIENT);
      checkExistingSolutionsWithUpdatedContract(con, scope, merchantList.get(con.AccountId), newItems);
    }
  }

  /**
   * <AUTHOR>
   * @date 14/09/2021
   * @description Block update of end date by throwing an AddError if date overlap
   * @param contractIns Contract where EndDate or StartDate has changed
   * @param scope
   * @param accountIns Account for which the contracts has changed
   * @param newItems Contract object from trigger
   *
   */
  public static void checkExistingSolutionsWithUpdatedContract(
    Contract contractIns,
    String scope,
    Account accountIns,
    Map<Id, Contract> newItems
  ) {
    Map<String, ER_Solution_Line_Item__c> accountSolutionsMap = new Map<String, ER_Solution_Line_Item__c>();

    for (ER_Solution_Line_Item__c solution : accountIns.Solution_Line_Items__r) {
      Contract newContract = newItems.get(contractIns.Id);
      if (
        String.isNotBlank(solution.ER_Solution__c) &&
        solution.ER_Contract__c != contractIns.Id &&
        String.isNotBlank(solution.ER_Solution__r.BU_External_Reference__c) &&
        Utils.checkDatesBetween2Dates(
          newContract.StartDate,
          newContract.EndDate,
          solution.ER_Contract__r.StartDate,
          solution.ER_Contract__r.EndDate
        ) &&
        solution.ER_Scope__c.contains(scope)
      ) {
        for (String bu_reference : solution.ER_Solution__r.BU_External_Reference__c.split('\\|')) {
          accountSolutionsMap.put(bu_reference, solution);
        }
      }
    }
    System.debug('//NGO accountSolutionsMap' + accountSolutionsMap);

    for (ER_ContractLineItem__c item : contractIns.ER_ContractLineItems__r) {
      System.debug('//NGO item' + item);
      if (String.isNotBlank(item.ER_Solution__c)) {
        for (String contractSolutionCode : item.ER_Solution__c.split('\\|')) {
          //if(accountSolutionsMap.containsKey(contractSolutionCode) AND SAME SCOPE)
          if (accountSolutionsMap.containsKey(contractSolutionCode))
            newItems.get(contractIns.Id)
              .addError(
                Label.LABS_SF_SOLUTION_EXIST_IN_ACTIVE_CONTRACT
                  .replace('{0}', item.ER_Product2__r.Name)
                  .replace('{1}', accountSolutionsMap.get(contractSolutionCode).ER_Contract__r.ContractNumber)
              );
        }
      }
    }
  }

  public static Map<Id, Account> getExistingSolutionByAccounts(List<Id> accountIds) {
    //Get ER_Account_Line_Item record Type Id
    String Account_Line_ItemRTId = Utils.getRecordTypeIdByDeveloperName(
      ER_Solution_Line_Item__c.SObjectType,
      'ER_Account_Line_Item'
    );

    return new Map<Id, Account>(
      [
        SELECT
          Id,
          Name,
          ER_BUPicklist__c,
          (
            SELECT
              Id,
              ER_Solution__c,
              ER_Solution__r.BU_External_Reference__c,
              ER_Contract__c,
              ER_Contract__r.ContractNumber,
              ER_Contract__r.EndDate,
              ER_Contract__r.StartDate,
              ER_Scope__c
            FROM Solution_Line_Items__r
            WHERE RecordTypeId = :Account_Line_ItemRTId AND ER_Solution__r.zqu__Category__c = 'Base Products'
          )
        FROM Account
        WHERE Id IN :accountIds
      ]
    );
  }
  /**
   * <AUTHOR>
   * @date 14/09/2021
   * @description Check if the solution to be added exists already on an active contract having the same type
   * @param accountIns Account
   * @param contractIns Contract
   * @return List of errors
   */
  public static List<APHU13_ErrorBeforeEdg> checkExistingSolutionsContractOnAccount(
    Account accountIns,
    Contract contractIns
  ) {
    Map<String, ER_Solution_Line_Item__c> accountSolutionsMap = new Map<String, ER_Solution_Line_Item__c>();
    List<APHU13_ErrorBeforeEdg> listError = new List<APHU13_ErrorBeforeEdg>();

    for (ER_Solution_Line_Item__c solution : accountIns.Solution_Line_Items__r) {
      if (
        String.isNotBlank(solution.ER_Solution__c) &&
        String.isNotBlank(solution.ER_Solution__r.BU_External_Reference__c)
      ) {
        for (String bu_reference : solution.ER_Solution__r.BU_External_Reference__c.split('\\|')) {
          accountSolutionsMap.put(bu_reference, solution);
        }
      }
    }

    for (ER_ContractLineItem__c item : contractIns.ER_ContractLineItems__r) {
      if (String.isNotBlank(item.ER_Solution__c))
        listError = populateErrorRecord(accountIns.Name, item, accountSolutionsMap);
    }

    return listError;
  }

  public static List<APHU13_ErrorBeforeEdg> populateErrorRecord(
    String accountName,
    ER_ContractLineItem__c item,
    Map<String, ER_Solution_Line_Item__c> accountSolutionsMap
  ) {
    List<APHU13_ErrorBeforeEdg> errors = new List<APHU13_ErrorBeforeEdg>();
    system.debug('//NGO accountSolutionsMap ' + accountSolutionsMap);
    for (String contractSolutionCode : item.ER_Solution__c.split('\\|')) {
      if (accountSolutionsMap.containsKey(contractSolutionCode)) {
        APHU13_ErrorBeforeEdg error = new APHU13_ErrorBeforeEdg();
        error.sObjName = 'Contract';
        error.sName = accountName;
        error.sMessage = Label.LABS_SF_SOLUTION_EXIST_IN_ACTIVE_CONTRACT.replace('{0}', item.ER_Product2__r.Name)
          .replace('{1}', accountSolutionsMap.get(contractSolutionCode).ER_Contract__r.ContractNumber);
        error.sLink = '/lightning/r/Contract/' + accountSolutionsMap.get(contractSolutionCode).ER_Contract__c + '/view';
        error.sSection = 'Existing Solutions';
        errors.add(error);
        break;
      }
    }
    return errors;
  }

  /*
   * LASFAR
   * 02/01/2020
   * isOfflineBU : Check if the BU is Offline and All Contract's products are synchronized.
   */
  @AuraEnabled
  public static Boolean isOfflineBU(Contract contractInst) {
    APER30_AutoEnrollment_Management.syncRecordBU = contractInst.ER_BUPicklist__c; // AAM 07.07.23 COUL-4145 (EDG Sync)
    Boolean isOfflineBU = APER06_WebserviceUtility.Offline_Sales_WS;
    if (isOfflineBU) {
      return isOfflineBU;
    }

    Boolean synchronizableProduct = false;
    Map<Id, ER_ContractLineItem__c> cliMap = getContractLineItems(contractInst.Id);

    for (ER_ContractLineItem__c cli : cliMap.values()) {
      if (!cli.ER_Product2__r.ER_Do_Not_Synchronize__c) {
        synchronizableProduct = true;
      }
    }

    return !synchronizableProduct;
  }

  /*
   * Dragos Avram
   * 03/03/2020
   * getFrameworkContract : get the framework and refferal contract and display in framework contract list LWC.
   */
  @AuraEnabled(cacheable=true)
  public static List<Contract> getFrameworkContract(String accountId) {
    List<Id> parentsId = APER12_Contract_Management.findTopLevelParent(accountId);
    List<Id> partnersAccountId = APER12_Contract_Management.getEligiblePartners(accountId);
    List<Contract> frameworkContract = [
      SELECT
        Id,
        ContractNumber,
        Status,
        ER_Contract_Type__c,
        StartDate,
        Account.Name,
        AccountId,
        (SELECT ER_Contract__c, Name, ER_Solution__c FROM ER_ContractLineItems__r)
      FROM Contract
      WHERE
        (((AccountId IN :parentsId
        OR AccountId = :accountId)
        AND ER_Contract_Type__c = 'Framework agreement')
        OR ((AccountId = :accountId
        OR AccountId IN :partnersAccountId)
        AND ER_Contract_Type__c = 'Referral agreement'))
        AND Status = :System.Label.LABS_SF_Contract_Status_Activated
      ORDER BY CreatedDate DESC
    ];
    return frameworkContract;
  }

  /*
   * Dragos Avram
   * 03/03/2020
   * getEligiblePartners : get the eligible account partners
   */
  public static List<Id> getEligiblePartners(String accountId) {
    List<Id> eligiblePartners = new List<Id>();
    Account accountsWithPartners = [
      SELECT id, (SELECT id, Role, ReversePartnerId, AccountToId FROM PartnersFrom)
      FROM Account
      WHERE Id = :accountId
      LIMIT 1
    ];
    for (Partner partnerObj : accountsWithPartners.PartnersFrom) {
      if (
        partnerObj.Role.equalsIgnoreCase('Trade association') ||
        partnerObj.Role.equalsIgnoreCase('Business partner (Decision maker)') ||
        partnerObj.Role.equalsIgnoreCase('Franchiser') ||
        partnerObj.Role.equalsIgnoreCase('Federation')
      ) {
        eligiblePartners.add(partnerObj.AccountToId);
      }
    }

    return eligiblePartners;
  }
  /*
   * Dragos Avram
   * 03/03/2020
   * findTopLevelParent : itterate through the account Parent=child hierarchy(from bottom to top)
   */
  public static List<Id> findTopLevelParent(String a) {
    Boolean isTopLevelAccount = false;
    account acct = new account();
    List<Id> parentAccount = new List<Id>();
    id currAcctId = a;

    while (!isTopLevelAccount) {
      acct = [SELECT Id, ParentId, ER_Decision_maker__c, Name FROM Account WHERE Id = :currAcctId LIMIT 1];
      if (acct.ParentID != null) {
        currAcctId = acct.ParentID;
        if (acct.ER_Decision_maker__c) {
          parentAccount.add(currAcctId);
        }
      } else {
        isTopLevelAccount = true;
        if (acct.ER_Decision_maker__c) {
          parentAccount.add(currAcctId);
        }
      }
    }
    return parentAccount;
  }
  /**
   * <AUTHOR>
   * @date 06/05/2021
   * @description Enfoce the following name convention {Contract.BU} - {Account.Name} - {Contract.Creation Date } - {sequence 01} - {Original Opportunity Name}
   * @param contracts
   */
  public static void setContractName(List<Contract> contracts) {
    Set<Id> setAccountsIds = new Set<Id>();
    Set<Id> setOpportunityIds = new Set<Id>();
    Map<Id, List<Contract>> mapAccountTempContracts = new Map<Id, List<Contract>>();
    Map<Id, Opportunity> mapOpportunity = new Map<Id, Opportunity>();

    for (Contract c : contracts) {
      setAccountsIds.add(c.AccountId);
      setOpportunityIds.add(c.ER_OpportunityId__c);
      if (!mapAccountTempContracts.containsKey(c.AccountId))
        mapAccountTempContracts.put(c.AccountId, new List<Contract>());
      mapAccountTempContracts.get(c.AccountId).add(c);
    }

    if (!setOpportunityIds.isEmpty())
      mapOpportunity = new Map<Id, Opportunity>(
        [SELECT Id, Name FROM Opportunity WHERE AccountId IN :mapAccountTempContracts.keySet()]
      );

    for (Account a : [
      SELECT
        Id,
        ER_Registration_Number__c,
        Name,
        (SELECT Id, Name, ContractNumber FROM Contracts ORDER BY ContractNumber ASC)
      FROM Account
      WHERE Id IN :setAccountsIds
    ]) {
      if (!a.Contracts.isEmpty()) {
        //Get Max Sequence
        Integer sequence = 0;
        for (Contract c : a.Contracts) {
          Integer currentSequence = (String.isNotBlank(c.Name) &&
            String.valueOf(c.Name.substringAfterLast('-')).isNumeric()
            ? Integer.valueOf(c.Name.substringAfterLast('-'))
            : 0);
          sequence = (sequence < currentSequence ? currentSequence : sequence);
        }

        for (Integer i = 0; i < mapAccountTempContracts.get(a.Id).size(); i++) {
          Contract c = mapAccountTempContracts.get(a.Id)[i];
          String originalOppName = String.isNotBlank(c.ER_OpportunityId__c) &&
            mapOpportunity.containsKey(c.ER_OpportunityId__c)
            ? '-' + Utils.getOriginalNameFromOpportunity(mapOpportunity.get(c.ER_OpportunityId__c).Name)
            : '';
          c.Name =
            c.ER_Business_Unit__c +
            '-' +
            a.Name.left(50) +
            '-' +
            Datetime.now().format('YYYY-MM-dd') +
            '-' +
            (i +
            1 +
            sequence);
        }
      } else {
        for (Integer i = 0; i < mapAccountTempContracts.get(a.Id).size(); i++) {
          Contract c = mapAccountTempContracts.get(a.Id)[i];
          String originalOppName = String.isNotBlank(c.ER_OpportunityId__c) &&
            mapOpportunity.containsKey(c.ER_OpportunityId__c)
            ? '-' + Utils.getOriginalNameFromOpportunity(mapOpportunity.get(c.ER_OpportunityId__c).Name)
            : '';
          c.Name =
            c.ER_Business_Unit__c +
            '-' +
            a.Name.left(50) +
            '-' +
            Datetime.now().format('YYYY-MM-dd') +
            '-' +
            (i + 1);
        }
      }
    }
  }

  /**
   * <AUTHOR>
   * @date 31/05/2021
   * @description Link the new contract to the quote signed (docusign pdf) document.
   * @param newContracts
   */
  public static void linkContractToSignedDocument(Map<id, Contract> newContracts) {
    Map<Id, Contract> mapQuoteContract = new Map<Id, Contract>();
    for (Contract c : [
      SELECT
        Id,
        ER_QuoteId__c,
        ER_QuoteId__r.ER_Is_Docusign_Deployed_BU__c,
        ER_ZuoraQuote__c,
        ER_ZuoraQuote__r.ER_TCs_Signed_Link__c
      FROM Contract
      WHERE Id IN :newContracts.keySet()
    ]) {
      if (c.ER_QuoteId__c != null && c.ER_QuoteId__r.ER_Is_Docusign_Deployed_BU__c) {
        mapQuoteContract.put(c.ER_QuoteId__c, c);
      }
      if (c.ER_ZuoraQuote__c != null && c.ER_ZuoraQuote__r.ER_TCs_Signed_Link__c != null) {
        mapQuoteContract.put(c.ER_ZuoraQuote__c, c);
      }
    }

    if (!mapQuoteContract.isEmpty()) {
      List<ContentDocumentLink> newLinks = new List<ContentDocumentLink>();
      for (ContentDocumentLink cdl : [
        SELECT
          Id,
          LinkedEntityId,
          ContentDocumentId,
          ShareType,
          Visibility,
          ContentDocument.Title,
          ContentDocument.FileExtension
        FROM ContentDocumentLink
        WHERE LinkedEntityId IN :mapQuoteContract.keySet()
      ]) {
        if (cdl.ContentDocument.FileExtension == Constants.PDF) {
          ContentDocumentLink newLink = cdl.clone(false, false, false, false);
          newLink.LinkedEntityId = mapQuoteContract.get(cdl.LinkedEntityId).Id;
          newLinks.add(newLink);
        }
      }

      if (!newLinks.isEmpty())
        Database.insert(newLinks, false);
    }
  }

  /**
   * @AUTHOR AAM
   * @date 02-JUN-2021
   * @description Check if the parent contract is Draft before Deleting contract line Item
   * @param contractLineItemList
   *
   * @return
   */
  public static void checkDraftParent(List<ER_ContractLineItem__c> contractLineItemList) {
    System.debug('###:checkDraftParent Start');

    for (ER_ContractLineItem__c cliInst : contractLineItemList) {
      if (cliInst.ER_Contract_Status__c != Label.LABS_SF_Contract_Status_Draft)
        cliInst.addError(Label.LABS_SF_ER_ContractLineItem_CannotBeDeleted);
      else if(APER10_User_Management.userBu == Constants.RO && cliInst.ER_Contract_Status__c == Label.LABS_SF_Contract_Status_Draft){
          //Add logic for RO Client Scope to prevent contract line items deletion for all Contract statuses, including Draft
          //DSA - 14/11/2023
        cliInst.addError(Label.LABS_SF_ER_ContractLineItem_CannotBeDeleted);      
      }
    }

    System.debug('###:checkDraftParent End');
  }

  /**
   * <AUTHOR>
   * @date 02/07/2021
   * @description Update the creation date in ios field for contract line items
   * @param newItems
   * @param oldItems
   */
  public static void syncCLIFlags(Map<Id, SObject> newItems, Map<Id, SObject> oldItems) {
    System.debug('### syncContractLineItems Start');

    Map<Id, ER_ContractLineItem__c> newCLI = (Map<Id, ER_ContractLineItem__c>) newItems;
    Map<Id, ER_ContractLineItem__c> oldCLI = (Map<Id, ER_ContractLineItem__c>) oldItems;

    // Loop through the Account lists and add fill the creation date in OS
    for (ER_ContractLineItem__c cli : newCLI.values()) {
      if (
        cli.ER_Sync_result__c != oldCLI.get(cli.Id).ER_Sync_result__c &&
        (cli.ER_Sync_result__c == 'synchronized' ||
        cli.ER_Sync_result__c == 'pending_synchronization') &&
        cli.ER_Creation_date_in_OS__c == null
      )
        cli.ER_Creation_date_in_OS__c = Datetime.now();
    }

    System.debug('### syncContractLineItems End');
  }

  /**
   * <AUTHOR>
   * @date 07/07/2021
   * @description Create a subscription after contract activation
   * @param newContracts
   * @param oldContracts
   */
  public static void createZuoraSubscription(Map<Id, Contract> newContracts, Map<Id, Contract> oldContracts) {
    Map<Id, Contract> mapQuoteContract = new Map<Id, Contract>();
    Set<String> bAccounts = new Set<String>();
    List<String> zuoraDeployedBU = Utils.splitValues(Utils.getGlobalSettingsValueFromKey('ZUORA_DEPLOYED_BUs'));
    List<Contract> newItems = new List<Contract>();

    for (Contract c : newContracts.values()) {
      if (zuoraDeployedBU.contains(Constants.ALL) || zuoraDeployedBU.contains(c.ER_BUPicklist__c))
        newItems.add(c);
    }

    if (!newItems.isEmpty()) {
      for (Contract newContr : [
        SELECT
          Id,
          Status,
          ER_ZuoraQuote__c,
          ER_ZuoraQuote__r.zqu__Account__c,
          ER_ZuoraQuote__r.zqu__ZuoraAccountID__c,
          ER_OpportunityId__r.ER_IsAmended__c,
          Subscription__c,
          ER_ZuoraQuote__r.zqu__PaymentTerm__c,
          ER_ZuoraQuote__r.zqu__Zuora_Account_Number__c,
          ER_BUPicklist__c
        FROM Contract
        WHERE ER_ZuoraQuote__c != NULL AND Id IN :newItems
      ]) {
        if (
          newContr.Status != oldContracts.get(newContr.Id).Status &&
          newContr.Status == Constants.ACTIVATED &&
          newContr.ER_ZuoraQuote__r.zqu__Account__c != null &&
          newContr.ER_ZuoraQuote__r.zqu__ZuoraAccountID__c != null
        ) {
          mapQuoteContract.put(newContr.Id, newContr);
          bAccounts.add(newContr.ER_ZuoraQuote__r.zqu__Zuora_Account_Number__c);
        }
      }

      if (!mapQuoteContract.isEmpty())
        System.enqueueJob(new ER_SendQuoteToZuora(mapQuoteContract, bAccounts));
    }
  }

  /**
   * <AUTHOR>
   * @date 02/09/2021
   * @description Activate Account if Contract has been activated
   * @param newContracts
   * @param oldContracts
   */
  public static void activateAccount(Map<Id, Contract> newContracts, Map<Id, Contract> oldContracts) {
    Set<Id> accounts = new Set<Id>();
    List<Account> accountsToBeUpdated = new List<Account>();

    for (Contract c : newContracts.values()) {
      if (c.Status == Label.LABS_SF_Contract_Status_Activated && c.Status != oldContracts.get(c.Id).Status) {
        accounts.add(c.AccountId);
      }
    }

    if (!accounts.isEmpty()) {
      for (Account a : [
        SELECT Id, ER_Status__c
        FROM Account
        WHERE Id IN :accounts AND ER_Status__c != :Label.LABS_SF_Account_Status_Active
      ]) {
        a.ER_Status__c = Label.LABS_SF_Account_Status_Active;
        accountsToBeUpdated.add(a);
      }

      if (!accountsToBeUpdated.isEmpty())
        Database.update(accountsToBeUpdated, false);
    }
  }

  /**
   * <AUTHOR>
   * @date 27/12/2023
   * @description Close the Opportunity as won if Contract has been activated
   * @param newContracts
   * @param oldContracts
   */
  public static void closeOpportunity(Map<Id, Contract> newContracts, Map<Id, Contract> oldContracts) {
    Set<Id> opportunities = new Set<Id>();
    List<Opportunity> OpportunitiesToBeUpdated = new List<Opportunity>();

    for (Contract c : newContracts.values()) {
      if (String.isNotBlank(c.ER_OpportunityId__c) && c.Status == Label.LABS_SF_Contract_Status_Activated && c.Status != oldContracts.get(c.Id).Status) {
        opportunities.add(c.ER_OpportunityId__c);
      }
    }

    if (!opportunities.isEmpty()) {
      for (Opportunity opp : [
              SELECT Id, StageName
              FROM Opportunity
              WHERE Id IN :opportunities AND StageName !=: Label.LABS_SF_Opp_Status_ClosedWon
      ]) {
        opp.StageName = Label.LABS_SF_Opp_Status_ClosedWon;
        opp.ER_Won_Reason__c = 'Other';
        OpportunitiesToBeUpdated.add(opp);
      }

      if (!OpportunitiesToBeUpdated.isEmpty())
        Database.update(OpportunitiesToBeUpdated, false);
    }
  }
  /**
   * <AUTHOR>
   * @date 24/03/2023
   * @description Sync Account to Smarter when contract is activated
   * @param newContracts updated contract
   * @param oldContracts old contract
   */
  public static void syncAccountToSmarter(Map<Id, Contract> newContracts, Map<Id, Contract> oldContracts) {
    Set<Id> accountSet = new Set<Id>();
    for (Contract c : newContracts.values()) {
      if (c.Status == Label.LABS_SF_Contract_Status_Activated && c.Status != oldContracts.get(c.Id).Status)
        accountSet.add(c.AccountId);
    }
    if (!accountSet.isEmpty())
      APER20_Account_Management.runSyncClientToSmarter(accountSet);
  }
  /**
   * <AUTHOR>
   * @date 12/07/2021
   * @description end a subscription after contract termination
   * @param newContracts
   * @param oldContracts
   */
  public static void endZuoraSubscription(Map<Id, Contract> newContracts, Map<Id, Contract> oldContracts) {
    List<Zuora__Subscription__c> listSubsToUpdate = new List<Zuora__Subscription__c>();

    for (Contract newContr : [
      SELECT Id, Status, Subscription__c, EndDate, ER_TerminationReason__c, (SELECT Id FROM Subscriptions__r)
      FROM Contract
      WHERE Subscription__c != NULL AND Id IN :newContracts.keySet()
    ]) {
      Contract oldContr = oldContracts.get(newContr.Id);

      if (newContr.Status != oldContr.Status && newContr.Status == 'Terminated') {
        listSubsToUpdate.add(
          new Zuora__Subscription__c(
            Id = newContr.Subscription__c,
            Zuora__SubscriptionEndDate__c = newContr.EndDate.addDays(1)
          )
        );

        for (Zuora__Subscription__c childSub : newContr.Subscriptions__r) {
          listSubsToUpdate.add(
            new Zuora__Subscription__c(Id = childSub.Id, Zuora__SubscriptionEndDate__c = newContr.EndDate.addDays(1))
          );
        }
      }
    }

    if (!listSubsToUpdate.isEmpty())
      Database.update(listSubsToUpdate, false);
  }

  /**
   * <AUTHOR>
   * @date 25/03/2022
   * @description Contract Trigger Framework
   * @param contractMap
   */
  public static void processContractBeforeDelete(Map<Id, Contract> contractMap) {
    for (Contract contractInst : contractMap.values()) {
      if (contractInst.Status != Constants.DRAFT) {
        contractInst.addError(Label.LABS_SF_Contract_CannotBeDeleted);
      }
    }
    deleteCLIFromContract(contractMap);
    deleteSLIFromContract(contractMap); // delete Solution line items
  }

  /**
   * <AUTHOR>
   * @date 28/03/2022
   * @description Contract Trigger Framework rework
   * @param newItems
   * @param oldItems
   */
  public static void createTasksAfterUpdate(Map<Id, Contract> newItems, Map<Id, Contract> oldItems) {
    List<Task> taskList = new List<Task>();
    Set<Id> quoteIds = new Set<Id>();
    List<String> excludedBUs = Utils.getGlobalSettingsValueFromKey(Constants.CREATE_TASK_FOR_CONTRACT_EXCLUDED_BUS)
      ?.split(Constants.SEMICOLON);
    Id ContractMerchantRT = Utils.getRecordTypeIdByDeveloperName(Contract.SObjectType, Constants.CONTRACT_MERCHANT_RT);

    for (Contract c : newItems.values()) {
      if (
        c.RecordTypeId == ContractMerchantRT &&
        c.Status != oldItems.get(c.Id)?.Status &&
        c.Status == Constants.ACTIVATED &&
        UserInfo.getUserType() != Constants.AUTOMATED_PROCESS &&
        excludedBUs != null &&
        (!excludedBUs.contains(c.ER_BUPicklist__c) && !excludedBUs.contains(Constants.ALL))
      ) {
        quoteIds.add(c.ER_QuoteId__c);
      }
    }

    if (!quoteIds.isEmpty()) {
      for (Quote q : [
        SELECT Id, ContractId, Account.Name, Status, ER_BUPicklist__c, ContactId, Contract.CreatedById
        FROM Quote
        WHERE Id IN :quoteIds AND ContactId != NULL
      ]) {
        String subject = Label.LABS_SF_Task_Subject_WelcomeLetter + ' ' + q.Account.Name;
        taskList.add(
          APER09_Task_Management.CreateTaskForContractWithoutInsert(
            subject,
            q.Contract.CreatedById,
            Date.today() + 1,
            q.ContractId,
            q.ContactId
          )
        );

        if (!taskList.isEmpty())
          Database.insert(taskList, false);
      }
    }
  }

  /**
   * <AUTHOR>
   * @date 29/08/2022
   * @description Activate Smarter Contract: if the created contract is linked to smartER Opportunity and the opportunity has been created by an ER Integration profile
   * @param newContracts
   */
  public static void activateContract(List<Contract> newContracts) {
    List<Contract> smarterContracts = new List<Contract>();
    Set<String> ranges = new Set<String>{
      '1-2',
      '3-5',
      '6-9',
      '10-19',
      '20-49',
      '50-99',
      '1 - 2',
      '3 - 5',
      '6 - 9',
      '10 - 19',
      '20 - 49',
      '50 - 99',
      '1 -2',
      '3 -5',
      '6 -9',
      '10 -19',
      '20 -49',
      '50 -99',
      '1- 2',
      '3- 5',
      '6- 9',
      '10- 19',
      '20- 49',
      '50- 99'
    };
    Map<Id, Contract> mapContracts = new Map<Id, Contract>(newContracts);
    Map<Id, Contract> contracts = new Map<Id, Contract>(
      APER12_Contract_Management.getContract(new List<Id>(mapContracts.keySet()))
    );

    for (Contract c : newContracts) {
      if (c.ER_SmartER_Context__c == true && c.ER_BUPicklist__c == Constants.FR)
        smarterContracts.add(c);
    }

    if (!smarterContracts.isEmpty()) {
      for (Contract c : [
        SELECT Id
        FROM Contract
        WHERE
          Id IN :smarterContracts
          AND ER_SmartER_Context__c = TRUE
          AND Status = :Constants.CONTRACT_STATUS_DRAFT
          AND (ER_ZuoraQuote__r.CreatedBy.Profile.Name = :Constants.PROFILE_ER_INTEGRATION
          OR (ER_BUPicklist__c = 'FR'
          AND Account.ER_Employee_Size_Range__c IN :ranges))
      ]) {
        APER12_Contract_Management.activateContractController(
          contracts.get(c.Id),
          contracts.get(c.Id).ER_OpportunityId__c,
          true
        );
      }
    }
  }

  /**
   * <AUTHOR>
   * @date 22/09/2022
   * @description Contract Task Creation
   */
  public static void contractTaskCreation(List<Contract> newContracts) {
    List<Task> taskList = new List<Task>();
    List<Contract> newItems = new List<Contract>();

    for (Contract c : newContracts) {
      if (!c.ER_SmartER_Context__c)
        newItems.add(c);
    }

    if (!newItems.isEmpty()) {
      for (Contract c : [
        SELECT
          Id,
          ER_OpportunityId__r.OwnerId,
          ER_OpportunityId__r.LeadSource,
          ER_OpportunityId__r.TECH_IsWeboffer__c,
          ER_OpportunityId__r.Description,
          Status
        FROM Contract
        WHERE Id IN :newItems AND ER_SmartER_Context__c = FALSE
      ]) {
        if (
          c.ER_OpportunityId__c != null &&
          c.Status == Constants.DRAFT &&
          c.ER_OpportunityId__r.LeadSource != Constants.AUTO_ENROLLMENT &&
          c.ER_OpportunityId__r.TECH_IsWeboffer__c == false
        ) {
          //Create Won Reason task on Opportunity
          Task newTask = new Task();
          newTask.ActivityDate = Date.today();
          newTask.Description = Label.TASK_OPP_WON_REASON_DESCRIPTION;
          newTask.OwnerId = c.ER_OpportunityId__r.OwnerId;
          newTask.Priority = Constants.NORMAL;
          newTask.Status = Constants.OPEN;
          newTask.Subject = Label.TASK_OPP_WON_REASON_SUBJECT;
          newTask.WhatId = c.ER_OpportunityId__r.Id;
          taskList.add(newTask);
        }
      }

      if (!taskList.isEmpty())
        Database.insert(taskList, false);
    }
  }

  /**
   * <AUTHOR>
   * @date 19/12/2022
   * @description Tick Contacts to be synced with Smarter
   * @param newContracts
   * @param oldContractsMap
   */
  //All contacts + financial centers flagged as to be synced with Smarter under the account linked to the activated contract (with SmartER Context) and created by Profile != IntegrationUser
  //=> To be Synced and The API is also Called  (via trigger)
  public static void markObjectsToBeSyncedWithSmarter(List<Contract> newContracts, Map<Id, Contract> oldContractsMap) {
    List<Id> accountIds = new List<Id>();
    List<Contact> contactsToUpdate = new List<Contact>();
    List<ER_Financial_Center__c> financialCentersToUpdate = new List<ER_Financial_Center__c>();
    List<Contract> newItems = new List<Contract>();

    for (Contract ctr : newContracts) {
      if (ctr.ER_SmartER_Context__c)
        newItems.add(ctr);
    }

    if (!newItems.isEmpty()) {
      for (Contract ctr : [
        SELECT Id, Status, AccountId, ER_ZuoraQuote__r.zqu__BillToContact__c
        FROM Contract
        WHERE
          Id IN :newItems
          AND ER_SmartER_Context__c = TRUE
          AND CreatedBy.Profile.Name != :Constants.PROFILE_ER_INTEGRATION
      ]) {
        if (
          oldContractsMap != null &&
          ctr.Status != oldContractsMap.get(ctr.Id).Status &&
          ctr.Status == Constants.ACTIVATED
        ) {
          accountIds.add(ctr.AccountId);
          if (ctr.ER_ZuoraQuote__r.zqu__BillToContact__c != null)
            contactsToUpdate.add(
              new Contact(Id = ctr.ER_ZuoraQuote__r.zqu__BillToContact__c, ER_To_sync_with_smarter__c = true)
            );
        }
      }

      for (ER_Financial_Center__c fc : [
        SELECT Id, ER_To_sync_with_smarter__c
        FROM ER_Financial_Center__c
        WHERE ER_Account_Name__c IN :accountIds AND ER_SmartER_Context__c = TRUE AND ER_To_sync_with_smarter__c = FALSE
      ]) {
        fc.ER_To_sync_with_smarter__c = true;
        financialCentersToUpdate.add(fc);
      }

      if (!contactsToUpdate.isEmpty())
        Database.update(contactsToUpdate, false);
      if (!financialCentersToUpdate.isEmpty())
        Database.update(financialCentersToUpdate, false);
    }
  }

  /**
   * <AUTHOR>
   * @date 17/01/2023
   * @description map some ZQuote fields to the linked Contract
   * @param newContracts
   */
  public static void mapFieldsFromZQuote(List<Contract> newContracts) {
    List<Contract> contractsToBeUpdated = new List<Contract>();
    for (Contract c : [
      SELECT
        Id,
        ER_ZuoraQuote__c,
        ER_ZuoraQuote__r.ER_Customer_Signed_Version__c,
        ER_ZuoraQuote__r.ER_TCs_Signed_Link__c,
        ER_ZuoraQuote__r.ER_Agreement_DateTime__c,
        ER_Customer_Signed_Version__c,
        ER_TCs_Signed_Link__c,
        ER_Agreement_DateTime__c
      FROM Contract
      WHERE
        Id IN :newContracts
        AND ER_SmartER_Context__c = TRUE
        AND ER_ZuoraQuote__c != NULL
        AND ER_ZuoraQuote__r.ER_Customer_Signed_Version__c != NULL
        AND ER_ZuoraQuote__r.ER_Agreement_DateTime__c != NULL
        AND ER_SmartER_Context__c = TRUE
    ]) {
      c.ER_Customer_Signed_Version__c = c.ER_ZuoraQuote__r.ER_Customer_Signed_Version__c;
      c.ER_Agreement_DateTime__c = c.ER_ZuoraQuote__r.ER_Agreement_DateTime__c;
      c.ER_TCs_Signed_Link__c = c.ER_ZuoraQuote__r.ER_TCs_Signed_Link__c;
      contractsToBeUpdated.add(c);
    }

    if (!contractsToBeUpdated.isEmpty())
      Database.update(contractsToBeUpdated, false);
  }
  
  /**
     * <AUTHOR>
     * @date 14/11/2023
     * @description Update ship to and bill to fields from related Account
     * USED BY RO FOR CLIENT SCOPE ONLY
   */

  public static void updateAddressFields(List<Contract> contracts) {
    Map<Id, List<Contract>> contractListByOpportunityIdMap = new Map<Id, List<Contract>>();
    for (Contract con : contracts) {
      if(con.ER_Business_Unit__c == Constants.RO && con.RecordTypeId == Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ER_Client_Contract_RT').getRecordTypeId()) {
        if (!contractListByOpportunityIdMap.containsKey(con.ER_OpportunityId__c)) contractListByOpportunityIdMap.put(con.ER_OpportunityId__c, new List<Contract>());
        contractListByOpportunityIdMap.get(con.ER_OpportunityId__c).add(con);
      }
    }

    for(Opportunity opp : [SELECT Id, Account.BillingStreet, Account.BillingCity, Account.BillingState, Account.BillingPostalCode, Account.BillingCountry
    FROM Opportunity WHERE Id IN :contractListByOpportunityIdMap.keySet()]){
      if(contractListByOpportunityIdMap.containsKey(opp.Id)){
        for(Contract c :contractListByOpportunityIdMap.get(opp.Id)){
          c.BillingStreet = opp.Account.BillingStreet;
          c.BillingCity = opp.Account.BillingCity;
          c.BillingState = opp.Account.BillingState;
          c.BillingPostalCode = opp.Account.BillingPostalCode;
          c.BillingCountry = opp.Account.BillingCountry;
          c.ShippingStreet = opp.Account.BillingStreet;
          c.ShippingCity = opp.Account.BillingCity;
          c.ShippingState = opp.Account.BillingState;
          c.ShippingPostalCode = opp.Account.BillingPostalCode;
          c.ShippingCountry = opp.Account.BillingCountry;
        }
      }
    }

  }
  
  /**
     * <AUTHOR>
     * @date 16/11/2023
     * @description Update Contract End Date based on the contract Duration - If evergreen keep empty
     * USED BY RO FOR CLIENT SCOPE ONLY
   */

  public static void updateContractEndDateRO(List<Contract> contracts, Map<Id, Contract> oldContractsMap) {

    for (Contract con : contracts) {
        if(con.ER_Business_Unit__c == Constants.RO && con.RecordTypeId == Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ER_Client_Contract_RT').getRecordTypeId() &&
          (!String.isBlank(con.ER_Contract_Duration__c) || (oldContractsMap != null && con.ER_Contract_Duration__c != oldContractsMap.get(con.Id).ER_Contract_Duration__c))) {

          if(con.ER_Contract_Duration__c == Constants.CONTRACT_EVERGREEN) con.EndDate = null;
          else if(con.ER_Contract_Duration__c.contains('month'))
                con.EndDate = con.StartDate.addMonths(retrieveNumericalValue(con.ER_Contract_Duration__c));
              else con.EndDate = con.StartDate.addYears(retrieveNumericalValue(con.ER_Contract_Duration__c));
       }
    }
  }

  public static Integer retrieveNumericalValue(String aValue){

    String numericValueString = aValue.replaceAll('[^0-9]', '');
    Integer numericValue = Integer.valueOf(numericValueString);
    return numericValue;

  }

  /**
     * <AUTHOR>
     * @date 12/12/2023
     * @description Activate Contracts Sync with BON : if a contract is created in SF from Opp and the contract is synchronized and activated from BON by data user
     * USED BY RO FOR CLIENT SCOPE ONLY
     *  LASFARO : Manage sli if batch size is set to 1, recommendation to use Talend for future SLI sync
   */
    public static void syncObjectsForActivatedContractFromBON(List<Contract> newContractsList, Map<Id, Contract> oldContractsMap){
        if(APER10_User_Management.userBU == Constants.RO && UtilsBypass.canTrigger('syncObjectsForActivatedContractFromBON')){
            //List<Distributed_Transaction_Event__e> eventsToPublish = new List<Distributed_Transaction_Event__e>();
          //List<Contract> bonContracts = new List<Contract>();
            //Map<Id, Contract> mapContracts = new Map<Id, Contract>(newContractsList);
            /*Map<Id, Contract> contracts = new Map<Id, Contract>(
                APER12_Contract_Management.getContract(new List<Id>(mapContracts.keySet()))
            );*/

          if(!newContractsList.isEmpty() && newContractsList[0].Status == Constants.ACTIVATED && oldContractsMap.get(newContractsList[0].Id).Status != Constants.ACTIVATED){
            if(newContractsList.size() > 1){
              for(Contract c : newContractsList){
                c.addError(Label.LABS_SF_Contract_Activation_Error);
              }
            }
            else{
              manageSolutionLineItems(newContractsList[0]);
            }
          }
        }                
    }
}
